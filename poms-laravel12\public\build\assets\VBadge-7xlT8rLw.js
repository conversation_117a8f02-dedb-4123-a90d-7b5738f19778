import{a8 as N,a9 as S,ag as B,ah as V,ai as h,aj as P,ak as x,al as T,ac as R,am as _,b as t,p as d,an as w,ae as I,ao as L,ap as X,af as A,aq as M,H as Y,I as $,X as j}from"./main-CWjS3hwz.js";import{m as q,M as D}from"./VImg-B7Lnz0wk.js";const F=S({bordered:Boolean,color:String,content:[Number,String],dot:Boolean,floating:Boolean,icon:M,inline:Boolean,label:{type:String,default:"$vuetify.badge"},max:[Number,String],modelValue:{type:Boolean,default:!0},offsetX:[Number,String],offsetY:[Number,String],textColor:String,...A(),...X({location:"top end"}),...L(),...I(),...w(),...q({transition:"scale-rotate-transition"})},"VBadge"),z=N()({name:"VBadge",inheritAttrs:!1,props:F(),setup(e,o){const{backgroundColorClasses:u,backgroundColorStyles:c}=B(()=>e.color),{roundedClasses:m}=V(e),{t:g}=h(),{textColorClasses:b,textColorStyles:f}=P(()=>e.textColor),{themeClasses:v}=x(),{locationStyles:C}=T(e,!0,a=>(e.floating?e.dot?2:4:e.dot?8:12)+(["top","bottom"].includes(a)?Number(e.offsetY??0):["left","right"].includes(a)?Number(e.offsetX??0):0));return R(()=>{const a=Number(e.content),n=!e.max||isNaN(a)?e.content:a<=Number(e.max)?a:`${e.max}+`,[k,y]=_(o.attrs,["aria-atomic","aria-label","aria-live","role","title"]);return t(e.tag,d({class:["v-badge",{"v-badge--bordered":e.bordered,"v-badge--dot":e.dot,"v-badge--floating":e.floating,"v-badge--inline":e.inline},e.class]},y,{style:e.style}),{default:()=>{var s,l;return[t("div",{class:"v-badge__wrapper"},[(l=(s=o.slots).default)==null?void 0:l.call(s),t(D,{transition:e.transition},{default:()=>{var i,r;return[Y(t("span",d({class:["v-badge__badge",v.value,u.value,m.value,b.value],style:[c.value,f.value,e.inline?{}:C.value],"aria-atomic":"true","aria-label":g(e.label,a),"aria-live":"polite",role:"status"},k),[e.dot?void 0:o.slots.badge?(r=(i=o.slots).badge)==null?void 0:r.call(i):e.icon?t(j,{icon:e.icon},null):n]),[[$,e.modelValue]])]}})])]}})}),{}}});export{z as V};
