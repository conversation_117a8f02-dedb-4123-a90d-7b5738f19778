import{d as w,r as d,w as b,D as g,bm as C,bU as _,g as k,o as B,f as E,y as S,h as D}from"./main-CWjS3hwz.js";const P=w({__name:"PerfectScrollbar",props:{tag:{default:"div"},options:{default:()=>({})}},emits:["scroll","ps-scroll-y","ps-scroll-x","ps-scroll-up","ps-scroll-down","ps-scroll-left","ps-scroll-right","ps-y-reach-start","ps-y-reach-end","ps-x-reach-start","ps-x-reach-end"],setup(h,{expose:v,emit:m}){const o=h,y=m,t=d(null),s=d(null);b(()=>o.options,()=>{n(),a()},{deep:!0}),g(()=>{t.value&&a()}),C(()=>{n()});function a(){t.value&&(s.value=new _(t.value,o.options),c())}function n(){s.value&&(c(!1),s.value.destroy(),s.value=null)}const x={scroll:e("scroll"),"ps-scroll-y":e("ps-scroll-y"),"ps-scroll-x":e("ps-scroll-x"),"ps-scroll-up":e("ps-scroll-up"),"ps-scroll-down":e("ps-scroll-down"),"ps-scroll-left":e("ps-scroll-left"),"ps-scroll-right":e("ps-scroll-right"),"ps-y-reach-start":e("ps-y-reach-start"),"ps-y-reach-end":e("ps-y-reach-end"),"ps-x-reach-start":e("ps-x-reach-start"),"ps-x-reach-end":e("ps-x-reach-end")};function e(l){return function(r){y(l,r)}}function c(l=!0){var r;(r=s.value)!=null&&r.element&&Object.entries(x).forEach(([p,u])=>{var f,i;l?(f=s.value)==null||f.element.addEventListener(p,u):(i=s.value)==null||i.element.removeEventListener(p,u)})}return v({ps:s}),(l,r)=>(B(),k(D(l.tag),{ref_key:"scrollbar",ref:t,class:"ps"},{default:E(()=>[S(l.$slots,"default")]),_:3},512))}});export{P as C};
