import{aM as Ot,r as z,aN as Mt,a8 as le,a9 as E,aa as oe,ai as ge,aO as $t,aw as Nt,aP as xe,aQ as J,aR as Ee,aS as ze,V as D,aT as Ve,W as H,ac as ue,b as u,aF as Et,an as Ht,ae as Kt,aU as jt,ao as Gt,aH as zt,aJ as Ut,af as Ue,aV as Wt,aq as Z,a0 as Y,p as N,aW as et,aX as _e,w as ae,F as ee,aY as De,aZ as qt,a_ as gt,a$ as ve,b0 as fe,b1 as He,b2 as We,aC as Jt,b3 as Yt,D as ht,b4 as X,aI as Xt,b5 as me,b6 as we,ad as Qt,b7 as tt,b8 as at,X as ce,b9 as Zt,aL as ea,s as te,N as he,L as be,ba as ta,bb as aa,bc as la,bd as Ce,be as Ie,bf as na,ag as ra,bg as oa,bh as ua,bi as qe,$ as Se,t as Ke,bj as Le,m as pe,bk as sa,d as ia,c as ca,e as Te,f as M,o as lt,g as da,l as fa}from"./main-CWjS3hwz.js";import{a as Oe}from"./index-Dq7h7Pqt.js";import{V as nt,b as rt,c as va}from"./VCard-y1UgCbHQ.js";import{V as ma}from"./VDialog-GhSSCIsG.js";import{V as ot}from"./VCardText-DnBTLtJ0.js";import{u as ga,a as je,m as ha}from"./VTextField-XbG62wJL.js";import{f as ba}from"./forwardRefs-B931MWyl.js";import{u as ya,m as pa,V as Sa,a as ut}from"./VList-CUNWEV6C.js";import{m as wa}from"./VImg-B7Lnz0wk.js";import{g as xa,a as ka}from"./VOverlay-zhJGflkj.js";import{V as Pa}from"./VMenu-ClqezPud.js";import{b as Ae,V as Va,a as Ia}from"./VForm-CPID1Qyc.js";import{V as Ta}from"./VAvatar-Ds2UYUGl.js";import{V as Je}from"./VChip-DuBqIAai.js";import{V as st,m as _a}from"./VTable-6Q--EYaT.js";import{V as Da}from"./VDivider-DaCbr10A.js";import{V as Ca,a as Me}from"./VRow-BqKZbSVo.js";import{V as Fa}from"./VSpacer-CAYcyQHy.js";import{b as it}from"./route-block-B_A1xBdJ.js";/* empty css              */function ct(e,l,t){return Object.keys(e).filter(a=>Ot(a)&&a.endsWith(l)).reduce((a,n)=>(a[n.slice(0,-l.length)]=r=>e[n](r,t(r)),a),{})}function Aa(){const e=z([]);Mt(()=>e.value=[]);function l(t,a){e.value[a]=t}return{refs:e,updateRef:l}}const Ba=E({activeColor:String,start:{type:[Number,String],default:1},modelValue:{type:Number,default:e=>e.start},disabled:Boolean,length:{type:[Number,String],default:1,validator:e=>e%1===0},totalVisible:[Number,String],firstIcon:{type:Z,default:"$first"},prevIcon:{type:Z,default:"$prev"},nextIcon:{type:Z,default:"$next"},lastIcon:{type:Z,default:"$last"},ariaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.root"},pageAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.page"},currentPageAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.currentPage"},firstAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.first"},previousAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.previous"},nextAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.next"},lastAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.last"},ellipsis:{type:String,default:"..."},showFirstLastPage:Boolean,...Wt(),...Ue(),...Ut(),...zt(),...Gt(),...jt(),...Kt({tag:"nav"}),...Ht(),...Et({variant:"text"})},"VPagination"),dt=le()({name:"VPagination",props:Ba(),emits:{"update:modelValue":e=>!0,first:e=>!0,prev:e=>!0,next:e=>!0,last:e=>!0},setup(e,l){let{slots:t,emit:a}=l;const n=oe(e,"modelValue"),{t:r,n:i}=ge(),{isRtl:o}=$t(),{themeClasses:c}=Nt(e),{width:d}=xe(),s=J(-1);Ee(void 0,{scoped:!0});const{resizeRef:v}=ze(x=>{if(!x.length)return;const{target:h,contentRect:P}=x[0],C=h.querySelector(".v-pagination__list > *");if(!C)return;const A=P.width,K=C.offsetWidth+parseFloat(getComputedStyle(C).marginRight)*2;s.value=S(A,K)}),m=D(()=>parseInt(e.length,10)),b=D(()=>parseInt(e.start,10)),y=D(()=>e.totalVisible!=null?parseInt(e.totalVisible,10):s.value>=0?s.value:S(d.value,58));function S(x,h){const P=e.showFirstLastPage?5:3;return Math.max(0,Math.floor(Number(((x-h*P)/h).toFixed(2))))}const g=D(()=>{if(m.value<=0||isNaN(m.value)||m.value>Number.MAX_SAFE_INTEGER)return[];if(y.value<=0)return[];if(y.value===1)return[n.value];if(m.value<=y.value)return Ve(m.value,b.value);const x=y.value%2===0,h=x?y.value/2:Math.floor(y.value/2),P=x?h:h+1,C=m.value-h;if(P-n.value>=0)return[...Ve(Math.max(1,y.value-1),b.value),e.ellipsis,m.value];if(n.value-C>=(x?1:0)){const A=y.value-1,K=m.value-A+b.value;return[b.value,e.ellipsis,...Ve(A,K)]}else{const A=Math.max(1,y.value-2),K=A===1?n.value:n.value-Math.ceil(A/2)+b.value;return[b.value,e.ellipsis,...Ve(A,K),e.ellipsis,m.value]}});function k(x,h,P){x.preventDefault(),n.value=h,P&&a(P,h)}const{refs:f,updateRef:V}=Aa();Ee({VPaginationBtn:{color:H(()=>e.color),border:H(()=>e.border),density:H(()=>e.density),size:H(()=>e.size),variant:H(()=>e.variant),rounded:H(()=>e.rounded),elevation:H(()=>e.elevation)}});const I=D(()=>g.value.map((x,h)=>{const P=C=>V(C,h);if(typeof x=="string")return{isActive:!1,key:`ellipsis-${h}`,page:x,props:{ref:P,ellipsis:!0,icon:!0,disabled:!0}};{const C=x===n.value;return{isActive:C,key:x,page:i(x),props:{ref:P,ellipsis:!1,icon:!0,disabled:!!e.disabled||Number(e.length)<2,color:C?e.activeColor:e.color,"aria-current":C,"aria-label":r(C?e.currentPageAriaLabel:e.pageAriaLabel,x),onClick:A=>k(A,x)}}}})),T=D(()=>{const x=!!e.disabled||n.value<=b.value,h=!!e.disabled||n.value>=b.value+m.value-1;return{first:e.showFirstLastPage?{icon:o.value?e.lastIcon:e.firstIcon,onClick:P=>k(P,b.value,"first"),disabled:x,"aria-label":r(e.firstAriaLabel),"aria-disabled":x}:void 0,prev:{icon:o.value?e.nextIcon:e.prevIcon,onClick:P=>k(P,n.value-1,"prev"),disabled:x,"aria-label":r(e.previousAriaLabel),"aria-disabled":x},next:{icon:o.value?e.prevIcon:e.nextIcon,onClick:P=>k(P,n.value+1,"next"),disabled:h,"aria-label":r(e.nextAriaLabel),"aria-disabled":h},last:e.showFirstLastPage?{icon:o.value?e.firstIcon:e.lastIcon,onClick:P=>k(P,b.value+m.value-1,"last"),disabled:h,"aria-label":r(e.lastAriaLabel),"aria-disabled":h}:void 0}});function U(){var h;const x=n.value-b.value;(h=f.value[x])==null||h.$el.focus()}function R(x){x.key===et.left&&!e.disabled&&n.value>Number(e.start)?(n.value=n.value-1,_e(U)):x.key===et.right&&!e.disabled&&n.value<b.value+m.value-1&&(n.value=n.value+1,_e(U))}return ue(()=>u(e.tag,{ref:v,class:["v-pagination",c.value,e.class],style:e.style,role:"navigation","aria-label":r(e.ariaLabel),onKeydown:R,"data-test":"v-pagination-root"},{default:()=>[u("ul",{class:"v-pagination__list"},[e.showFirstLastPage&&u("li",{key:"first",class:"v-pagination__first","data-test":"v-pagination-first"},[t.first?t.first(T.value.first):u(Y,N({_as:"VPaginationBtn"},T.value.first),null)]),u("li",{key:"prev",class:"v-pagination__prev","data-test":"v-pagination-prev"},[t.prev?t.prev(T.value.prev):u(Y,N({_as:"VPaginationBtn"},T.value.prev),null)]),I.value.map((x,h)=>u("li",{key:x.key,class:["v-pagination__item",{"v-pagination__item--is-active":x.isActive}],"data-test":"v-pagination-item"},[t.item?t.item(x):u(Y,N({_as:"VPaginationBtn"},x.props),{default:()=>[x.page]})])),u("li",{key:"next",class:"v-pagination__next","data-test":"v-pagination-next"},[t.next?t.next(T.value.next):u(Y,N({_as:"VPaginationBtn"},T.value.next),null)]),e.showFirstLastPage&&u("li",{key:"last",class:"v-pagination__last","data-test":"v-pagination-last"},[t.last?t.last(T.value.last):u(Y,N({_as:"VPaginationBtn"},T.value.last),null)])])]})),{}}}),Ra=E({renderless:Boolean,...Ue()},"VVirtualScrollItem"),La=le()({name:"VVirtualScrollItem",inheritAttrs:!1,props:Ra(),emits:{"update:height":e=>!0},setup(e,l){let{attrs:t,emit:a,slots:n}=l;const{resizeRef:r,contentRect:i}=ze(void 0,"border");ae(()=>{var o;return(o=i.value)==null?void 0:o.height},o=>{o!=null&&a("update:height",o)}),ue(()=>{var o,c;return e.renderless?u(ee,null,[(o=n.default)==null?void 0:o.call(n,{itemRef:r})]):u("div",N({ref:r,class:["v-virtual-scroll__item",e.class],style:e.style},t),[(c=n.default)==null?void 0:c.call(n)])})}}),Oa=-1,Ma=1,$e=100,$a=E({itemHeight:{type:[Number,String],default:null},itemKey:{type:[String,Array,Function],default:null},height:[Number,String]},"virtual");function Na(e,l){const t=xe(),a=J(0);De(()=>{a.value=parseFloat(e.itemHeight||0)});const n=J(0),r=J(Math.ceil((parseInt(e.height)||t.height.value)/(a.value||16))||1),i=J(0),o=J(0),c=z(),d=z();let s=0;const{resizeRef:v,contentRect:m}=ze();De(()=>{v.value=c.value});const b=D(()=>{var p;return c.value===document.documentElement?t.height.value:((p=m.value)==null?void 0:p.height)||parseInt(e.height)||0}),y=D(()=>!!(c.value&&d.value&&b.value&&a.value));let S=Array.from({length:l.value.length}),g=Array.from({length:l.value.length});const k=J(0);let f=-1;function V(p){return S[p]||a.value}const I=qt(()=>{const p=performance.now();g[0]=0;const _=l.value.length;for(let L=1;L<=_-1;L++)g[L]=(g[L-1]||0)+V(L-1);k.value=Math.max(k.value,performance.now()-p)},k),T=ae(y,p=>{p&&(T(),s=d.value.offsetTop,I.immediate(),W(),~f&&_e(()=>{He&&window.requestAnimationFrame(()=>{de(f),f=-1})}))});gt(()=>{I.clear()});function U(p,_){const L=S[p],$=a.value;a.value=$?Math.min(a.value,_):_,(L!==_||$!==a.value)&&(S[p]=_,I())}function R(p){return p=ve(p,0,l.value.length-1),g[p]||0}function x(p){return Ea(g,p)}let h=0,P=0,C=0;ae(b,(p,_)=>{_&&(W(),p<_&&requestAnimationFrame(()=>{P=0,W()}))});let A=-1;function K(){if(!c.value||!d.value)return;const p=c.value.scrollTop,_=performance.now();_-C>500?(P=Math.sign(p-h),s=d.value.offsetTop):P=p-h,h=p,C=_,window.clearTimeout(A),A=window.setTimeout(Q,500),W()}function Q(){!c.value||!d.value||(P=0,C=0,window.clearTimeout(A),W())}let ne=-1;function W(){cancelAnimationFrame(ne),ne=requestAnimationFrame(re)}function re(){if(!c.value||!b.value)return;const p=h-s,_=Math.sign(P),L=Math.max(0,p-$e),$=ve(x(L),0,l.value.length),F=p+b.value+$e,O=ve(x(F)+1,$+1,l.value.length);if((_!==Oa||$<n.value)&&(_!==Ma||O>r.value)){const B=R(n.value)-R($),j=R(O)-R(r.value);Math.max(B,j)>$e?(n.value=$,r.value=O):($<=0&&(n.value=$),O>=l.value.length&&(r.value=O))}i.value=R(n.value),o.value=R(l.value.length)-R(r.value)}function de(p){const _=R(p);!c.value||p&&!_?f=p:c.value.scrollTop=_}const w=D(()=>l.value.slice(n.value,r.value).map((p,_)=>{const L=_+n.value;return{raw:p,index:L,key:fe(p,e.itemKey,L)}}));return ae(l,()=>{S=Array.from({length:l.value.length}),g=Array.from({length:l.value.length}),I.immediate(),W()},{deep:1}),{calculateVisibleItems:W,containerRef:c,markerRef:d,computedItems:w,paddingTop:i,paddingBottom:o,scrollToIndex:de,handleScroll:K,handleScrollend:Q,handleItemResize:U}}function Ea(e,l){let t=e.length-1,a=0,n=0,r=null,i=-1;if(e[t]<l)return t;for(;a<=t;)if(n=a+t>>1,r=e[n],r>l)t=n-1;else if(r<l)i=n,a=n+1;else return r===l?n:a;return i}const Ha=E({items:{type:Array,default:()=>[]},renderless:Boolean,...$a(),...Ue(),...Xt()},"VVirtualScroll"),Ka=le()({name:"VVirtualScroll",props:Ha(),setup(e,l){let{slots:t}=l;const a=We("VVirtualScroll"),{dimensionStyles:n}=Jt(e),{calculateVisibleItems:r,containerRef:i,markerRef:o,handleScroll:c,handleScrollend:d,handleItemResize:s,scrollToIndex:v,paddingTop:m,paddingBottom:b,computedItems:y}=Na(e,H(()=>e.items));return Yt(()=>e.renderless,()=>{function S(){var f,V;const k=(arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1)?"addEventListener":"removeEventListener";i.value===document.documentElement?(document[k]("scroll",c,{passive:!0}),document[k]("scrollend",d)):((f=i.value)==null||f[k]("scroll",c,{passive:!0}),(V=i.value)==null||V[k]("scrollend",d))}ht(()=>{i.value=xa(a.vnode.el,!0),S(!0)}),gt(S)}),ue(()=>{const S=y.value.map(g=>u(La,{key:g.key,renderless:e.renderless,"onUpdate:height":k=>s(g.index,k)},{default:k=>{var f;return(f=t.default)==null?void 0:f.call(t,{item:g.raw,index:g.index,...k})}}));return e.renderless?u(ee,null,[u("div",{ref:o,class:"v-virtual-scroll__spacer",style:{paddingTop:X(m.value)}},null),S,u("div",{class:"v-virtual-scroll__spacer",style:{paddingBottom:X(b.value)}},null)]):u("div",{ref:i,class:["v-virtual-scroll",e.class],onScrollPassive:c,onScrollend:d,style:[n.value,e.style]},[u("div",{ref:o,class:"v-virtual-scroll__container",style:{paddingTop:X(m.value),paddingBottom:X(b.value)}},[S])])}),{calculateVisibleItems:r,scrollToIndex:v}}});function ja(e,l){const t=J(!1);let a;function n(o){cancelAnimationFrame(a),t.value=!0,a=requestAnimationFrame(()=>{a=requestAnimationFrame(()=>{t.value=!1})})}async function r(){await new Promise(o=>requestAnimationFrame(o)),await new Promise(o=>requestAnimationFrame(o)),await new Promise(o=>requestAnimationFrame(o)),await new Promise(o=>{if(t.value){const c=ae(t,()=>{c(),o()})}else o()})}async function i(o){var s,v;if(o.key==="Tab"&&((s=l.value)==null||s.focus()),!["PageDown","PageUp","Home","End"].includes(o.key))return;const c=(v=e.value)==null?void 0:v.$el;if(!c)return;(o.key==="Home"||o.key==="End")&&c.scrollTo({top:o.key==="Home"?0:c.scrollHeight,behavior:"smooth"}),await r();const d=c.querySelectorAll(":scope > :not(.v-virtual-scroll__spacer)");if(o.key==="PageDown"||o.key==="Home"){const m=c.getBoundingClientRect().top;for(const b of d)if(b.getBoundingClientRect().top>=m){b.focus();break}}else{const m=c.getBoundingClientRect().bottom;for(const b of[...d].reverse())if(b.getBoundingClientRect().bottom<=m){b.focus();break}}}return{onScrollPassive:n,onKeydown:i}}const Ga=E({chips:Boolean,closableChips:Boolean,closeText:{type:String,default:"$vuetify.close"},openText:{type:String,default:"$vuetify.open"},eager:Boolean,hideNoData:Boolean,hideSelected:Boolean,listProps:{type:Object},menu:Boolean,menuIcon:{type:Z,default:"$dropdown"},menuProps:{type:Object},multiple:Boolean,noDataText:{type:String,default:"$vuetify.noDataText"},openOnClear:Boolean,itemColor:String,...pa({itemChildren:!1})},"Select"),za=E({...Ga(),...Qt(ha({modelValue:null,role:"combobox"}),["validationValue","dirty","appendInnerIcon"]),...wa({transition:{component:ka}})},"VSelect"),Ye=le()({name:"VSelect",props:za(),emits:{"update:focused":e=>!0,"update:modelValue":e=>!0,"update:menu":e=>!0},setup(e,l){let{slots:t}=l;const{t:a}=ge(),n=z(),r=z(),i=z(),{items:o,transformIn:c,transformOut:d}=ya(e),s=oe(e,"modelValue",[],w=>c(w===null?[null]:me(w)),w=>{const p=d(w);return e.multiple?p:p[0]??null}),v=D(()=>typeof e.counterValue=="function"?e.counterValue(s.value):typeof e.counterValue=="number"?e.counterValue:s.value.length),m=ga(e),b=D(()=>s.value.map(w=>w.value)),y=J(!1);let S="",g;const k=D(()=>e.hideSelected?o.value.filter(w=>!s.value.some(p=>(e.valueComparator||we)(p,w))):o.value),f=D(()=>e.hideNoData&&!k.value.length||m.isReadonly.value||m.isDisabled.value),V=oe(e,"menu"),I=D({get:()=>V.value,set:w=>{var p;V.value&&!w&&((p=r.value)!=null&&p.ΨopenChildren.size)||w&&f.value||(V.value=w)}}),T=H(()=>I.value?e.closeText:e.openText),U=D(()=>{var w;return{...e.menuProps,activatorProps:{...((w=e.menuProps)==null?void 0:w.activatorProps)||{},"aria-haspopup":"listbox"}}}),R=z(),x=ja(R,n);function h(w){e.openOnClear&&(I.value=!0)}function P(){f.value||(I.value=!I.value)}function C(w){tt(w)&&A(w)}function A(w){var $,F;if(!w.key||m.isReadonly.value)return;["Enter"," ","ArrowDown","ArrowUp","Home","End"].includes(w.key)&&w.preventDefault(),["Enter","ArrowDown"," "].includes(w.key)&&(I.value=!0),["Escape","Tab"].includes(w.key)&&(I.value=!1),w.key==="Home"?($=R.value)==null||$.focus("first"):w.key==="End"&&((F=R.value)==null||F.focus("last"));const p=1e3;if(!tt(w))return;const _=performance.now();_-g>p&&(S=""),S+=w.key.toLowerCase(),g=_;const L=o.value.find(O=>O.title.toLowerCase().startsWith(S));if(L!==void 0){s.value=[L];const O=k.value.indexOf(L);He&&window.requestAnimationFrame(()=>{var B;O>=0&&((B=i.value)==null||B.scrollToIndex(O))})}}function K(w){let p=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;if(!w.props.disabled)if(e.multiple){const _=s.value.findIndex($=>(e.valueComparator||we)($.value,w.value)),L=p??!~_;if(~_){const $=L?[...s.value,w]:[...s.value];$.splice(_,1),s.value=$}else L&&(s.value=[...s.value,w])}else{const _=p!==!1;s.value=_?[w]:[],_e(()=>{I.value=!1})}}function Q(w){var p;(p=R.value)!=null&&p.$el.contains(w.relatedTarget)||(I.value=!1)}function ne(){var w;e.eager&&((w=i.value)==null||w.calculateVisibleItems())}function W(){var w;y.value&&((w=n.value)==null||w.focus())}function re(w){y.value=!0}function de(w){if(w==null)s.value=[];else if(at(n.value,":autofill")||at(n.value,":-webkit-autofill")){const p=o.value.find(_=>_.title===w);p&&K(p)}else n.value&&(n.value.value="")}return ae(I,()=>{if(!e.hideSelected&&I.value&&s.value.length){const w=k.value.findIndex(p=>s.value.some(_=>(e.valueComparator||we)(_.value,p.value)));He&&window.requestAnimationFrame(()=>{var p;w>=0&&((p=i.value)==null||p.scrollToIndex(w))})}}),ae(()=>e.items,(w,p)=>{I.value||y.value&&!p.length&&w.length&&(I.value=!0)}),ue(()=>{const w=!!(e.chips||t.chip),p=!!(!e.hideNoData||k.value.length||t["prepend-item"]||t["append-item"]||t["no-data"]),_=s.value.length>0,L=je.filterProps(e),$=_||!y.value&&e.label&&!e.persistentPlaceholder?void 0:e.placeholder;return u(je,N({ref:n},L,{modelValue:s.value.map(F=>F.props.value).join(", "),"onUpdate:modelValue":de,focused:y.value,"onUpdate:focused":F=>y.value=F,validationValue:s.externalValue,counterValue:v.value,dirty:_,class:["v-select",{"v-select--active-menu":I.value,"v-select--chips":!!e.chips,[`v-select--${e.multiple?"multiple":"single"}`]:!0,"v-select--selected":s.value.length,"v-select--selection-slot":!!t.selection},e.class],style:e.style,inputmode:"none",placeholder:$,"onClick:clear":h,"onMousedown:control":P,onBlur:Q,onKeydown:A,"aria-label":a(T.value),title:a(T.value)}),{...t,default:()=>u(ee,null,[u(Pa,N({ref:r,modelValue:I.value,"onUpdate:modelValue":F=>I.value=F,activator:"parent",contentClass:"v-select__content",disabled:f.value,eager:e.eager,maxHeight:310,openOnClick:!1,closeOnContentClick:!1,transition:e.transition,onAfterEnter:ne,onAfterLeave:W},U.value),{default:()=>[p&&u(Sa,N({ref:R,selected:b.value,selectStrategy:e.multiple?"independent":"single-independent",onMousedown:F=>F.preventDefault(),onKeydown:C,onFocusin:re,tabindex:"-1","aria-live":"polite","aria-label":`${e.label}-list`,color:e.itemColor??e.color},x,e.listProps),{default:()=>{var F,O,B;return[(F=t["prepend-item"])==null?void 0:F.call(t),!k.value.length&&!e.hideNoData&&(((O=t["no-data"])==null?void 0:O.call(t))??u(ut,{key:"no-data",title:a(e.noDataText)},null)),u(Ka,{ref:i,renderless:!0,items:k.value,itemKey:"value"},{default:j=>{var ye;let{item:G,index:ie,itemRef:q}=j;const se=N(G.props,{ref:q,key:G.value,onClick:()=>K(G,null)});return((ye=t.item)==null?void 0:ye.call(t,{item:G,index:ie,props:se}))??u(ut,N(se,{role:"option"}),{prepend:ke=>{let{isSelected:Pe}=ke;return u(ee,null,[e.multiple&&!e.hideSelected?u(Ae,{key:G.value,modelValue:Pe,ripple:!1,tabindex:"-1"},null):void 0,G.props.prependAvatar&&u(Ta,{image:G.props.prependAvatar},null),G.props.prependIcon&&u(ce,{icon:G.props.prependIcon},null)])}})}}),(B=t["append-item"])==null?void 0:B.call(t)]}})]}),s.value.map((F,O)=>{function B(q){q.stopPropagation(),q.preventDefault(),K(F,!1)}const j={"onClick:close":B,onKeydown(q){q.key!=="Enter"&&q.key!==" "||(q.preventDefault(),q.stopPropagation(),B(q))},onMousedown(q){q.preventDefault(),q.stopPropagation()},modelValue:!0,"onUpdate:modelValue":void 0},G=w?!!t.chip:!!t.selection,ie=G?Zt(w?t.chip({item:F,index:O,props:j}):t.selection({item:F,index:O})):void 0;if(!(G&&!ie))return u("div",{key:F.value,class:"v-select__selection"},[w?t.chip?u(ea,{key:"chip-defaults",defaults:{VChip:{closable:e.closableChips,size:"small",text:F.title}}},{default:()=>[ie]}):u(Je,N({key:"chip",closable:e.closableChips,size:"small",text:F.title,disabled:F.props.disabled},j),null):ie??u("span",{class:"v-select__selection-text"},[F.title,e.multiple&&O<s.value.length-1&&u("span",{class:"v-select__selection-comma"},[te(",")])])])})]),"append-inner":function(){var j,G;for(var F=arguments.length,O=new Array(F),B=0;B<F;B++)O[B]=arguments[B];return u(ee,null,[(j=t["append-inner"])==null?void 0:j.call(t,...O),e.menuIcon?u(ce,{class:"v-select__menu-icon",color:(G=n.value)==null?void 0:G.fieldIconColor,icon:e.menuIcon},null):void 0])}})}),ba({isFocused:y,menu:I,select:K},n)}}),Ua=E({page:{type:[Number,String],default:1},itemsPerPage:{type:[Number,String],default:10}},"DataTable-paginate"),bt=Symbol.for("vuetify:data-table-pagination");function Wa(e){const l=oe(e,"page",void 0,a=>Number(a??1)),t=oe(e,"itemsPerPage",void 0,a=>Number(a??10));return{page:l,itemsPerPage:t}}function qa(e){const{page:l,itemsPerPage:t,itemsLength:a}=e,n=D(()=>t.value===-1?0:t.value*(l.value-1)),r=D(()=>t.value===-1?a.value:Math.min(a.value,n.value+t.value)),i=D(()=>t.value===-1||a.value===0?1:Math.ceil(a.value/t.value));ae([l,i],()=>{l.value>i.value&&(l.value=i.value)});function o(m){t.value=m,l.value=1}function c(){l.value=ve(l.value+1,1,i.value)}function d(){l.value=ve(l.value-1,1,i.value)}function s(m){l.value=ve(m,1,i.value)}const v={page:l,itemsPerPage:t,startIndex:n,stopIndex:r,pageCount:i,itemsLength:a,nextPage:c,prevPage:d,setPage:s,setItemsPerPage:o};return be(bt,v),v}function Ja(){const e=he(bt);if(!e)throw new Error("Missing pagination!");return e}function Ya(e){const l=We("usePaginatedItems"),{items:t,startIndex:a,stopIndex:n,itemsPerPage:r}=e,i=D(()=>r.value<=0?t.value:t.value.slice(a.value,n.value));return ae(i,o=>{l.emit("update:currentItems",o)},{immediate:!0}),{paginatedItems:i}}const yt=E({prevIcon:{type:Z,default:"$prev"},nextIcon:{type:Z,default:"$next"},firstIcon:{type:Z,default:"$first"},lastIcon:{type:Z,default:"$last"},itemsPerPageText:{type:String,default:"$vuetify.dataFooter.itemsPerPageText"},pageText:{type:String,default:"$vuetify.dataFooter.pageText"},firstPageLabel:{type:String,default:"$vuetify.dataFooter.firstPage"},prevPageLabel:{type:String,default:"$vuetify.dataFooter.prevPage"},nextPageLabel:{type:String,default:"$vuetify.dataFooter.nextPage"},lastPageLabel:{type:String,default:"$vuetify.dataFooter.lastPage"},itemsPerPageOptions:{type:Array,default:()=>[{value:10,title:"10"},{value:25,title:"25"},{value:50,title:"50"},{value:100,title:"100"},{value:-1,title:"$vuetify.dataFooter.itemsPerPageAll"}]},showCurrentPage:Boolean},"VDataTableFooter"),ft=le()({name:"VDataTableFooter",props:yt(),setup(e,l){let{slots:t}=l;const{t:a}=ge(),{page:n,pageCount:r,startIndex:i,stopIndex:o,itemsLength:c,itemsPerPage:d,setItemsPerPage:s}=Ja(),v=D(()=>e.itemsPerPageOptions.map(m=>typeof m=="number"?{value:m,title:m===-1?a("$vuetify.dataFooter.itemsPerPageAll"):String(m)}:{...m,title:isNaN(Number(m.title))?a(m.title):m.title}));return ue(()=>{var b;const m=dt.filterProps(e);return u("div",{class:"v-data-table-footer"},[(b=t.prepend)==null?void 0:b.call(t),u("div",{class:"v-data-table-footer__items-per-page"},[u("span",null,[a(e.itemsPerPageText)]),u(Ye,{items:v.value,modelValue:d.value,"onUpdate:modelValue":y=>s(Number(y)),density:"compact",variant:"outlined","hide-details":!0},null)]),u("div",{class:"v-data-table-footer__info"},[u("div",null,[a(e.pageText,c.value?i.value+1:0,o.value,c.value)])]),u("div",{class:"v-data-table-footer__pagination"},[u(dt,N({modelValue:n.value,"onUpdate:modelValue":y=>n.value=y,density:"comfortable","first-aria-label":e.firstPageLabel,"last-aria-label":e.lastPageLabel,length:r.value,"next-aria-label":e.nextPageLabel,"previous-aria-label":e.prevPageLabel,rounded:!0,"show-first-last-page":!0,"total-visible":e.showCurrentPage?1:0,variant:"plain"},m),null)])])}),{}}}),Fe=ta({align:{type:String,default:"start"},fixed:Boolean,fixedOffset:[Number,String],height:[Number,String],lastFixed:Boolean,noPadding:Boolean,tag:String,width:[Number,String],maxWidth:[Number,String],nowrap:Boolean},(e,l)=>{let{slots:t}=l;const a=e.tag??"td";return u(a,{class:["v-data-table__td",{"v-data-table-column--fixed":e.fixed,"v-data-table-column--last-fixed":e.lastFixed,"v-data-table-column--no-padding":e.noPadding,"v-data-table-column--nowrap":e.nowrap},`v-data-table-column--align-${e.align}`],style:{height:X(e.height),width:X(e.width),maxWidth:X(e.maxWidth),left:X(e.fixedOffset||null)}},{default:()=>{var n;return[(n=t.default)==null?void 0:n.call(t)]}})}),Xa=E({headers:Array},"DataTable-header"),pt=Symbol.for("vuetify:data-table-headers"),St={title:"",sortable:!1},Qa={...St,width:48};function Za(){const l=(arguments.length>0&&arguments[0]!==void 0?arguments[0]:[]).map(t=>({element:t,priority:0}));return{enqueue:(t,a)=>{let n=!1;for(let r=0;r<l.length;r++)if(l[r].priority>a){l.splice(r,0,{element:t,priority:a}),n=!0;break}n||l.push({element:t,priority:a})},size:()=>l.length,count:()=>{let t=0;if(!l.length)return 0;const a=Math.floor(l[0].priority);for(let n=0;n<l.length;n++)Math.floor(l[n].priority)===a&&(t+=1);return t},dequeue:()=>l.shift()}}function Ge(e){let l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];if(!e.children)l.push(e);else for(const t of e.children)Ge(t,l);return l}function wt(e){let l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:new Set;for(const t of e)t.key&&l.add(t.key),t.children&&wt(t.children,l);return l}function el(e){if(e.key){if(e.key==="data-table-group")return St;if(["data-table-expand","data-table-select"].includes(e.key))return Qa}}function Xe(e){let l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return e.children?Math.max(l,...e.children.map(t=>Xe(t,l+1))):l}function tl(e){let l=!1;function t(r){let i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(r)if(i&&(r.fixed=!0),r.fixed)if(r.children)for(let o=r.children.length-1;o>=0;o--)t(r.children[o],!0);else l?isNaN(Number(r.width))?la(`Multiple fixed columns should have a static width (key: ${r.key})`):r.minWidth=Math.max(Number(r.width)||0,Number(r.minWidth)||0):r.lastFixed=!0,l=!0;else if(r.children)for(let o=r.children.length-1;o>=0;o--)t(r.children[o]);else l=!1}for(let r=e.length-1;r>=0;r--)t(e[r]);function a(r){let i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;if(!r)return i;if(r.children){r.fixedOffset=i;for(const o of r.children)i=a(o,i)}else r.fixed&&(r.fixedOffset=i,i+=parseFloat(r.width||"0")||0);return i}let n=0;for(const r of e)n=a(r,n)}function al(e,l){const t=[];let a=0;const n=Za(e);for(;n.size()>0;){let i=n.count();const o=[];let c=1;for(;i>0;){const{element:d,priority:s}=n.dequeue(),v=l-a-Xe(d);if(o.push({...d,rowspan:v??1,colspan:d.children?Ge(d).length:1}),d.children)for(const m of d.children){const b=s%1+c/Math.pow(10,a+2);n.enqueue(m,a+v+b)}c+=1,i-=1}a+=1,t.push(o)}return{columns:e.map(i=>Ge(i)).flat(),headers:t}}function xt(e){const l=[];for(const t of e){const a={...el(t),...t},n=a.key??(typeof a.value=="string"?a.value:null),r=a.value??n??null,i={...a,key:n,value:r,sortable:a.sortable??(a.key!=null||!!a.sort),children:a.children?xt(a.children):void 0};l.push(i)}return l}function ll(e,l){const t=z([]),a=z([]),n=z({}),r=z({}),i=z({});De(()=>{var S,g,k;const d=(e.headers||Object.keys(e.items[0]??{}).map(f=>({key:f,title:aa(f)}))).slice(),s=wt(d);(S=l==null?void 0:l.groupBy)!=null&&S.value.length&&!s.has("data-table-group")&&d.unshift({key:"data-table-group",title:"Group"}),(g=l==null?void 0:l.showSelect)!=null&&g.value&&!s.has("data-table-select")&&d.unshift({key:"data-table-select"}),(k=l==null?void 0:l.showExpand)!=null&&k.value&&!s.has("data-table-expand")&&d.push({key:"data-table-expand"});const v=xt(d);tl(v);const m=Math.max(...v.map(f=>Xe(f)))+1,b=al(v,m);t.value=b.headers,a.value=b.columns;const y=b.headers.flat(1);for(const f of y)f.key&&(f.sortable&&(f.sort&&(n.value[f.key]=f.sort),f.sortRaw&&(r.value[f.key]=f.sortRaw)),f.filter&&(i.value[f.key]=f.filter))});const o={headers:t,columns:a,sortFunctions:n,sortRawFunctions:r,filterFunctions:i};return be(pt,o),o}function Be(){const e=he(pt);if(!e)throw new Error("Missing headers!");return e}const nl={showSelectAll:!1,allSelected:()=>[],select:e=>{var a;let{items:l,value:t}=e;return new Set(t?[(a=l[0])==null?void 0:a.value]:[])},selectAll:e=>{let{selected:l}=e;return l}},kt={showSelectAll:!0,allSelected:e=>{let{currentPage:l}=e;return l},select:e=>{let{items:l,value:t,selected:a}=e;for(const n of l)t?a.add(n.value):a.delete(n.value);return a},selectAll:e=>{let{value:l,currentPage:t,selected:a}=e;return kt.select({items:t,value:l,selected:a})}},Pt={showSelectAll:!0,allSelected:e=>{let{allItems:l}=e;return l},select:e=>{let{items:l,value:t,selected:a}=e;for(const n of l)t?a.add(n.value):a.delete(n.value);return a},selectAll:e=>{let{value:l,allItems:t,selected:a}=e;return Pt.select({items:t,value:l,selected:a})}},rl=E({showSelect:Boolean,selectStrategy:{type:[String,Object],default:"page"},modelValue:{type:Array,default:()=>[]},valueComparator:{type:Function,default:we}},"DataTable-select"),Vt=Symbol.for("vuetify:data-table-selection");function ol(e,l){let{allItems:t,currentPage:a}=l;const n=oe(e,"modelValue",e.modelValue,f=>new Set(me(f).map(V=>{var I;return((I=t.value.find(T=>e.valueComparator(V,T.value)))==null?void 0:I.value)??V})),f=>[...f.values()]),r=D(()=>t.value.filter(f=>f.selectable)),i=D(()=>a.value.filter(f=>f.selectable)),o=D(()=>{if(typeof e.selectStrategy=="object")return e.selectStrategy;switch(e.selectStrategy){case"single":return nl;case"all":return Pt;case"page":default:return kt}}),c=J(null);function d(f){return me(f).every(V=>n.value.has(V.value))}function s(f){return me(f).some(V=>n.value.has(V.value))}function v(f,V){const I=o.value.select({items:f,value:V,selected:new Set(n.value)});n.value=I}function m(f,V,I){const T=[];if(V=V??a.value.findIndex(U=>U.value===f.value),e.selectStrategy!=="single"&&(I!=null&&I.shiftKey)&&c.value!==null){const[U,R]=[c.value,V].sort((x,h)=>x-h);T.push(...a.value.slice(U,R+1).filter(x=>x.selectable))}else T.push(f),c.value=V;v(T,!d([f]))}function b(f){const V=o.value.selectAll({value:f,allItems:r.value,currentPage:i.value,selected:new Set(n.value)});n.value=V}const y=D(()=>n.value.size>0),S=D(()=>{const f=o.value.allSelected({allItems:r.value,currentPage:i.value});return!!f.length&&d(f)}),g=H(()=>o.value.showSelectAll),k={toggleSelect:m,select:v,selectAll:b,isSelected:d,isSomeSelected:s,someSelected:y,allSelected:S,showSelectAll:g,lastSelectedIndex:c,selectStrategy:o};return be(Vt,k),k}function Re(){const e=he(Vt);if(!e)throw new Error("Missing selection!");return e}const ul=E({sortBy:{type:Array,default:()=>[]},customKeySort:Object,multiSort:Boolean,mustSort:Boolean},"DataTable-sort"),It=Symbol.for("vuetify:data-table-sort");function sl(e){const l=oe(e,"sortBy"),t=H(()=>e.mustSort),a=H(()=>e.multiSort);return{sortBy:l,mustSort:t,multiSort:a}}function il(e){const{sortBy:l,mustSort:t,multiSort:a,page:n}=e,r=c=>{if(c.key==null)return;let d=l.value.map(v=>({...v}))??[];const s=d.find(v=>v.key===c.key);s?s.order==="desc"?t.value&&d.length===1?s.order="asc":d=d.filter(v=>v.key!==c.key):s.order="desc":a.value?d.push({key:c.key,order:"asc"}):d=[{key:c.key,order:"asc"}],l.value=d,n&&(n.value=1)};function i(c){return!!l.value.find(d=>d.key===c.key)}const o={sortBy:l,toggleSort:r,isSorted:i};return be(It,o),o}function Tt(){const e=he(It);if(!e)throw new Error("Missing sort!");return e}function cl(e,l,t,a){const n=ge();return{sortedItems:D(()=>{var i,o;return t.value.length?dl(l.value,t.value,n.current.value,{transform:a==null?void 0:a.transform,sortFunctions:{...e.customKeySort,...(i=a==null?void 0:a.sortFunctions)==null?void 0:i.value},sortRawFunctions:(o=a==null?void 0:a.sortRawFunctions)==null?void 0:o.value}):l.value})}}function dl(e,l,t,a){const n=new Intl.Collator(t,{sensitivity:"accent",usage:"sort"});return e.map(i=>[i,a!=null&&a.transform?a.transform(i):i]).sort((i,o)=>{var c,d;for(let s=0;s<l.length;s++){let v=!1;const m=l[s].key,b=l[s].order??"asc";if(b===!1)continue;let y=Ce(i[1],m),S=Ce(o[1],m),g=i[0].raw,k=o[0].raw;if(b==="desc"&&([y,S]=[S,y],[g,k]=[k,g]),(c=a==null?void 0:a.sortRawFunctions)!=null&&c[m]){const f=a.sortRawFunctions[m](g,k);if(f==null)continue;if(v=!0,f)return f}if((d=a==null?void 0:a.sortFunctions)!=null&&d[m]){const f=a.sortFunctions[m](y,S);if(f==null)continue;if(v=!0,f)return f}if(!v){if(y instanceof Date&&S instanceof Date)return y.getTime()-S.getTime();if([y,S]=[y,S].map(f=>f!=null?f.toString().toLocaleLowerCase():f),y!==S)return Ie(y)&&Ie(S)?0:Ie(y)?-1:Ie(S)?1:!isNaN(y)&&!isNaN(S)?Number(y)-Number(S):n.compare(y,S)}}return 0}).map(i=>{let[o]=i;return o})}const _t=E({color:String,disableSort:Boolean,fixedHeader:Boolean,multiSort:Boolean,sortAscIcon:{type:Z,default:"$sortAsc"},sortDescIcon:{type:Z,default:"$sortDesc"},headerProps:{type:Object},sticky:Boolean,...qe(),...ua()},"VDataTableHeaders"),vt=le()({name:"VDataTableHeaders",props:_t(),setup(e,l){let{slots:t}=l;const{t:a}=ge(),{toggleSort:n,sortBy:r,isSorted:i}=Tt(),{someSelected:o,allSelected:c,selectAll:d,showSelectAll:s}=Re(),{columns:v,headers:m}=Be(),{loaderClasses:b}=na(e);function y(x,h){if(!(!(e.sticky||e.fixedHeader)&&!x.fixed))return{position:"sticky",left:x.fixed?X(x.fixedOffset):void 0,top:e.sticky||e.fixedHeader?`calc(var(--v-table-header-height) * ${h})`:void 0}}function S(x){const h=r.value.find(P=>P.key===x.key);return h?h.order==="asc"?e.sortAscIcon:e.sortDescIcon:e.sortAscIcon}const{backgroundColorClasses:g,backgroundColorStyles:k}=ra(()=>e.color),{displayClasses:f,mobile:V}=xe(e),I=D(()=>({headers:m.value,columns:v.value,toggleSort:n,isSorted:i,sortBy:r.value,someSelected:o.value,allSelected:c.value,selectAll:d,getSortIcon:S})),T=D(()=>["v-data-table__th",{"v-data-table__th--sticky":e.sticky||e.fixedHeader},f.value,b.value]),U=x=>{let{column:h,x:P,y:C}=x;const A=h.key==="data-table-select"||h.key==="data-table-expand",K=N(e.headerProps??{},h.headerProps??{});return u(Fe,N({tag:"th",align:h.align,class:[{"v-data-table__th--sortable":h.sortable&&!e.disableSort,"v-data-table__th--sorted":i(h),"v-data-table__th--fixed":h.fixed},...T.value],style:{width:X(h.width),minWidth:X(h.minWidth),maxWidth:X(h.maxWidth),...y(h,C)},colspan:h.colspan,rowspan:h.rowspan,onClick:h.sortable?()=>n(h):void 0,fixed:h.fixed,nowrap:h.nowrap,lastFixed:h.lastFixed,noPadding:A},K),{default:()=>{var W;const Q=`header.${h.key}`,ne={column:h,selectAll:d,isSorted:i,toggleSort:n,sortBy:r.value,someSelected:o.value,allSelected:c.value,getSortIcon:S};return t[Q]?t[Q](ne):h.key==="data-table-select"?((W=t["header.data-table-select"])==null?void 0:W.call(t,ne))??(s.value&&u(Ae,{modelValue:c.value,indeterminate:o.value&&!c.value,"onUpdate:modelValue":d},null)):u("div",{class:"v-data-table-header__content"},[u("span",null,[h.title]),h.sortable&&!e.disableSort&&u(ce,{key:"icon",class:"v-data-table-header__sort-icon",icon:S(h)},null),e.multiSort&&i(h)&&u("div",{key:"badge",class:["v-data-table-header__sort-badge",...g.value],style:k.value},[r.value.findIndex(re=>re.key===h.key)+1])])}})},R=()=>{const x=D(()=>v.value.filter(P=>(P==null?void 0:P.sortable)&&!e.disableSort)),h=D(()=>{if(v.value.find(C=>C.key==="data-table-select")!=null)return c.value?"$checkboxOn":o.value?"$checkboxIndeterminate":"$checkboxOff"});return u(Fe,N({tag:"th",class:[...T.value],colspan:m.value.length+1},e.headerProps),{default:()=>[u("div",{class:"v-data-table-header__content"},[u(Ye,{chips:!0,class:"v-data-table__td-sort-select",clearable:!0,density:"default",items:x.value,label:a("$vuetify.dataTable.sortBy"),multiple:e.multiSort,variant:"underlined","onClick:clear":()=>r.value=[],appendIcon:h.value,"onClick:append":()=>d(!c.value)},{...t,chip:P=>{var C;return u(Je,{onClick:(C=P.item.raw)!=null&&C.sortable?()=>n(P.item.raw):void 0,onMousedown:A=>{A.preventDefault(),A.stopPropagation()}},{default:()=>[P.item.title,u(ce,{class:["v-data-table__td-sort-icon",i(P.item.raw)&&"v-data-table__td-sort-icon-active"],icon:S(P.item.raw),size:"small"},null)]})}})])]})};ue(()=>V.value?u("tr",null,[u(R,null,null)]):u(ee,null,[t.headers?t.headers(I.value):m.value.map((x,h)=>u("tr",null,[x.map((P,C)=>u(U,{column:P,x:C,y:h},null))])),e.loading&&u("tr",{class:"v-data-table-progress"},[u("th",{colspan:v.value.length},[u(oa,{name:"v-data-table-progress",absolute:!0,active:!0,color:typeof e.loading=="boolean"?void 0:e.loading,indeterminate:!0},{default:t.loader})])])]))}}),fl=E({groupBy:{type:Array,default:()=>[]}},"DataTable-group"),Dt=Symbol.for("vuetify:data-table-group");function vl(e){return{groupBy:oe(e,"groupBy")}}function ml(e){const{disableSort:l,groupBy:t,sortBy:a}=e,n=z(new Set),r=D(()=>t.value.map(s=>({...s,order:s.order??!1})).concat(l!=null&&l.value?[]:a.value));function i(s){return n.value.has(s.id)}function o(s){const v=new Set(n.value);i(s)?v.delete(s.id):v.add(s.id),n.value=v}function c(s){function v(m){const b=[];for(const y of m.items)"type"in y&&y.type==="group"?b.push(...v(y)):b.push(y);return[...new Set(b)]}return v({items:s})}const d={sortByWithGroups:r,toggleGroup:o,opened:n,groupBy:t,extractRows:c,isGroupOpen:i};return be(Dt,d),d}function Ct(){const e=he(Dt);if(!e)throw new Error("Missing group!");return e}function gl(e,l){if(!e.length)return[];const t=new Map;for(const a of e){const n=Ce(a.raw,l);t.has(n)||t.set(n,[]),t.get(n).push(a)}return t}function Ft(e,l){let t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"root";if(!l.length)return[];const n=gl(e,l[0]),r=[],i=l.slice(1);return n.forEach((o,c)=>{const d=l[0],s=`${a}_${d}_${c}`;r.push({depth:t,id:s,key:d,value:c,items:i.length?Ft(o,i,t+1,s):o,type:"group"})}),r}function At(e,l){const t=[];for(const a of e)"type"in a&&a.type==="group"?(a.value!=null&&t.push(a),(l.has(a.id)||a.value==null)&&t.push(...At(a.items,l))):t.push(a);return t}function hl(e,l,t){return{flatItems:D(()=>{if(!l.value.length)return e.value;const n=Ft(e.value,l.value.map(r=>r.key));return At(n,t.value)})}}const bl=E({item:{type:Object,required:!0}},"VDataTableGroupHeaderRow"),yl=le()({name:"VDataTableGroupHeaderRow",props:bl(),setup(e,l){let{slots:t}=l;const{isGroupOpen:a,toggleGroup:n,extractRows:r}=Ct(),{isSelected:i,isSomeSelected:o,select:c}=Re(),{columns:d}=Be(),s=D(()=>r([e.item]));return()=>u("tr",{class:"v-data-table-group-header-row",style:{"--v-data-table-group-header-row-depth":e.item.depth}},[d.value.map(v=>{var m,b;if(v.key==="data-table-group"){const y=a(e.item)?"$expand":"$next",S=()=>n(e.item);return((m=t["data-table-group"])==null?void 0:m.call(t,{item:e.item,count:s.value.length,props:{icon:y,onClick:S}}))??u(Fe,{class:"v-data-table-group-header-row__column"},{default:()=>[u(Y,{size:"small",variant:"text",icon:y,onClick:S},null),u("span",null,[e.item.value]),u("span",null,[te("("),s.value.length,te(")")])]})}if(v.key==="data-table-select"){const y=i(s.value),S=o(s.value)&&!y,g=k=>c(s.value,k);return((b=t["data-table-select"])==null?void 0:b.call(t,{props:{modelValue:y,indeterminate:S,"onUpdate:modelValue":g}}))??u("td",null,[u(Ae,{modelValue:y,indeterminate:S,"onUpdate:modelValue":g},null)])}return u("td",null,null)})])}}),pl=E({expandOnClick:Boolean,showExpand:Boolean,expanded:{type:Array,default:()=>[]}},"DataTable-expand"),Bt=Symbol.for("vuetify:datatable:expanded");function Sl(e){const l=H(()=>e.expandOnClick),t=oe(e,"expanded",e.expanded,o=>new Set(o),o=>[...o.values()]);function a(o,c){const d=new Set(t.value);c?d.add(o.value):d.delete(o.value),t.value=d}function n(o){return t.value.has(o.value)}function r(o){a(o,!n(o))}const i={expand:a,expanded:t,expandOnClick:l,isExpanded:n,toggleExpand:r};return be(Bt,i),i}function Rt(){const e=he(Bt);if(!e)throw new Error("foo");return e}const wl=E({index:Number,item:Object,cellProps:[Object,Function],onClick:Le(),onContextmenu:Le(),onDblclick:Le(),...qe()},"VDataTableRow"),xl=le()({name:"VDataTableRow",props:wl(),setup(e,l){let{slots:t}=l;const{displayClasses:a,mobile:n}=xe(e,"v-data-table__tr"),{isSelected:r,toggleSelect:i,someSelected:o,allSelected:c,selectAll:d}=Re(),{isExpanded:s,toggleExpand:v}=Rt(),{toggleSort:m,sortBy:b,isSorted:y}=Tt(),{columns:S}=Be();ue(()=>u("tr",{class:["v-data-table__tr",{"v-data-table__tr--clickable":!!(e.onClick||e.onContextmenu||e.onDblclick)},a.value],onClick:e.onClick,onContextmenu:e.onContextmenu,onDblclick:e.onDblclick},[e.item&&S.value.map((g,k)=>{const f=e.item,V=`item.${g.key}`,I=`header.${g.key}`,T={index:e.index,item:f.raw,internalItem:f,value:Ce(f.columns,g.key),column:g,isSelected:r,toggleSelect:i,isExpanded:s,toggleExpand:v},U={column:g,selectAll:d,isSorted:y,toggleSort:m,sortBy:b.value,someSelected:o.value,allSelected:c.value,getSortIcon:()=>""},R=typeof e.cellProps=="function"?e.cellProps({index:T.index,item:T.item,internalItem:T.internalItem,value:T.value,column:g}):e.cellProps,x=typeof g.cellProps=="function"?g.cellProps({index:T.index,item:T.item,internalItem:T.internalItem,value:T.value}):g.cellProps;return u(Fe,N({align:g.align,class:{"v-data-table__td--expanded-row":g.key==="data-table-expand","v-data-table__td--select-row":g.key==="data-table-select"},fixed:g.fixed,fixedOffset:g.fixedOffset,lastFixed:g.lastFixed,maxWidth:n.value?void 0:g.maxWidth,noPadding:g.key==="data-table-select"||g.key==="data-table-expand",nowrap:g.nowrap,width:n.value?void 0:g.width},R,x),{default:()=>{var P,C,A,K;if(g.key==="data-table-select")return((P=t["item.data-table-select"])==null?void 0:P.call(t,{...T,props:{disabled:!f.selectable,modelValue:r([f]),onClick:Se(()=>i(f),["stop"])}}))??u(Ae,{disabled:!f.selectable,modelValue:r([f]),onClick:Se(Q=>i(f,e.index,Q),["stop"])},null);if(g.key==="data-table-expand")return((C=t["item.data-table-expand"])==null?void 0:C.call(t,{...T,props:{icon:s(f)?"$collapse":"$expand",size:"small",variant:"text",onClick:Se(()=>v(f),["stop"])}}))??u(Y,{icon:s(f)?"$collapse":"$expand",size:"small",variant:"text",onClick:Se(()=>v(f),["stop"])},null);if(t[V]&&!n.value)return t[V](T);const h=Ke(T.value);return n.value?u(ee,null,[u("div",{class:"v-data-table__td-title"},[((A=t[I])==null?void 0:A.call(t,U))??g.title]),u("div",{class:"v-data-table__td-value"},[((K=t[V])==null?void 0:K.call(t,T))??h])]):h}})})]))}}),Lt=E({loading:[Boolean,String],loadingText:{type:String,default:"$vuetify.dataIterator.loadingText"},hideNoData:Boolean,items:{type:Array,default:()=>[]},noDataText:{type:String,default:"$vuetify.noDataText"},rowProps:[Object,Function],cellProps:[Object,Function],...qe()},"VDataTableRows"),mt=le()({name:"VDataTableRows",inheritAttrs:!1,props:Lt(),setup(e,l){let{attrs:t,slots:a}=l;const{columns:n}=Be(),{expandOnClick:r,toggleExpand:i,isExpanded:o}=Rt(),{isSelected:c,toggleSelect:d}=Re(),{toggleGroup:s,isGroupOpen:v}=Ct(),{t:m}=ge(),{mobile:b}=xe(e);return ue(()=>{var y,S;return e.loading&&(!e.items.length||a.loading)?u("tr",{class:"v-data-table-rows-loading",key:"loading"},[u("td",{colspan:n.value.length},[((y=a.loading)==null?void 0:y.call(a))??m(e.loadingText)])]):!e.loading&&!e.items.length&&!e.hideNoData?u("tr",{class:"v-data-table-rows-no-data",key:"no-data"},[u("td",{colspan:n.value.length},[((S=a["no-data"])==null?void 0:S.call(a))??m(e.noDataText)])]):u(ee,null,[e.items.map((g,k)=>{var I;if(g.type==="group"){const T={index:k,item:g,columns:n.value,isExpanded:o,toggleExpand:i,isSelected:c,toggleSelect:d,toggleGroup:s,isGroupOpen:v};return a["group-header"]?a["group-header"](T):u(yl,N({key:`group-header_${g.id}`,item:g},ct(t,":group-header",()=>T)),a)}const f={index:k,item:g.raw,internalItem:g,columns:n.value,isExpanded:o,toggleExpand:i,isSelected:c,toggleSelect:d},V={...f,props:N({key:`item_${g.key??g.index}`,onClick:r.value?()=>{i(g)}:void 0,index:k,item:g,cellProps:e.cellProps,mobile:b.value},ct(t,":row",()=>f),typeof e.rowProps=="function"?e.rowProps({item:f.item,index:f.index,internalItem:f.internalItem}):e.rowProps)};return u(ee,{key:V.props.key},[a.item?a.item(V):u(xl,V.props,a),o(g)&&((I=a["expanded-row"])==null?void 0:I.call(a,f))])})])}),{}}}),kl=E({items:{type:Array,default:()=>[]},itemValue:{type:[String,Array,Function],default:"id"},itemSelectable:{type:[String,Array,Function],default:null},rowProps:[Object,Function],cellProps:[Object,Function],returnObject:Boolean},"DataTable-items");function Pl(e,l,t,a){const n=e.returnObject?l:fe(l,e.itemValue),r=fe(l,e.itemSelectable,!0),i=a.reduce((o,c)=>(c.key!=null&&(o[c.key]=fe(l,c.value)),o),{});return{type:"item",key:e.returnObject?fe(l,e.itemValue):n,index:t,value:n,selectable:r,columns:i,raw:l}}function Vl(e,l,t){return l.map((a,n)=>Pl(e,a,n,t))}function Il(e,l){return{items:D(()=>Vl(e,e.items,l.value))}}function Tl(e){let{page:l,itemsPerPage:t,sortBy:a,groupBy:n,search:r}=e;const i=We("VDataTable"),o=()=>({page:l.value,itemsPerPage:t.value,sortBy:a.value,groupBy:n.value,search:r.value});let c=null;ae(o,d=>{we(c,d)||(c&&c.search!==d.search&&(l.value=1),i.emit("update:options",d),c=d)},{deep:!0,immediate:!0})}const _l=(e,l,t)=>{if(e==null||l==null)return-1;if(!l.length)return 0;e=e.toString().toLocaleLowerCase(),l=l.toString().toLocaleLowerCase();const a=[];let n=e.indexOf(l);for(;~n;)a.push([n,n+l.length]),n=e.indexOf(l,n+l.length);return a.length?a:-1};function Ne(e,l){if(!(e==null||typeof e=="boolean"||e===-1))return typeof e=="number"?[[e,e+l.length]]:Array.isArray(e[0])?e:[e]}const Dl=E({customFilter:Function,customKeyFilter:Object,filterKeys:[Array,String],filterMode:{type:String,default:"intersection"},noFilter:Boolean},"filter");function Cl(e,l,t){var o;const a=[],n=(t==null?void 0:t.default)??_l,r=t!=null&&t.filterKeys?me(t.filterKeys):!1,i=Object.keys((t==null?void 0:t.customKeyFilter)??{}).length;if(!(e!=null&&e.length))return a;e:for(let c=0;c<e.length;c++){const[d,s=d]=me(e[c]),v={},m={};let b=-1;if((l||i>0)&&!(t!=null&&t.noFilter)){if(typeof d=="object"){const g=r||Object.keys(s);for(const k of g){const f=fe(s,k),V=(o=t==null?void 0:t.customKeyFilter)==null?void 0:o[k];if(b=V?V(f,l,d):n(f,l,d),b!==-1&&b!==!1)V?v[k]=Ne(b,l):m[k]=Ne(b,l);else if((t==null?void 0:t.filterMode)==="every")continue e}}else b=n(d,l,d),b!==-1&&b!==!1&&(m.title=Ne(b,l));const y=Object.keys(m).length,S=Object.keys(v).length;if(!y&&!S||(t==null?void 0:t.filterMode)==="union"&&S!==i&&!y||(t==null?void 0:t.filterMode)==="intersection"&&(S!==i||!y))continue}a.push({index:c,matches:{...m,...v}})}return a}function Fl(e,l,t,a){const n=J([]),r=J(new Map),i=D(()=>a!=null&&a.transform?pe(l).map(c=>[c,a.transform(c)]):pe(l));De(()=>{const c=typeof t=="function"?t():pe(t),d=typeof c!="string"&&typeof c!="number"?"":String(c),s=Cl(i.value,d,{customKeyFilter:{...e.customKeyFilter,...pe(a==null?void 0:a.customKeyFilter)},default:e.customFilter,filterKeys:e.filterKeys,filterMode:e.filterMode,noFilter:e.noFilter}),v=pe(l),m=[],b=new Map;s.forEach(y=>{let{index:S,matches:g}=y;const k=v[S];m.push(k),b.set(k.value,g)}),n.value=m,r.value=b});function o(c){return r.value.get(c.value)}return{filteredItems:n,filteredMatches:r,getMatches:o}}const Al=E({...Lt(),hideDefaultBody:Boolean,hideDefaultFooter:Boolean,hideDefaultHeader:Boolean,width:[String,Number],search:String,...pl(),...fl(),...Xa(),...kl(),...rl(),...ul(),..._t(),..._a()},"DataTable"),Bl=E({...Ua(),...Al(),...Dl(),...yt()},"VDataTable"),Rl=le()({name:"VDataTable",props:Bl(),emits:{"update:modelValue":e=>!0,"update:page":e=>!0,"update:itemsPerPage":e=>!0,"update:sortBy":e=>!0,"update:options":e=>!0,"update:groupBy":e=>!0,"update:expanded":e=>!0,"update:currentItems":e=>!0},setup(e,l){let{attrs:t,slots:a}=l;const{groupBy:n}=vl(e),{sortBy:r,multiSort:i,mustSort:o}=sl(e),{page:c,itemsPerPage:d}=Wa(e),{disableSort:s}=sa(e),{columns:v,headers:m,sortFunctions:b,sortRawFunctions:y,filterFunctions:S}=ll(e,{groupBy:n,showSelect:H(()=>e.showSelect),showExpand:H(()=>e.showExpand)}),{items:g}=Il(e,v),k=H(()=>e.search),{filteredItems:f}=Fl(e,g,k,{transform:j=>j.columns,customKeyFilter:S}),{toggleSort:V}=il({sortBy:r,multiSort:i,mustSort:o,page:c}),{sortByWithGroups:I,opened:T,extractRows:U,isGroupOpen:R,toggleGroup:x}=ml({groupBy:n,sortBy:r,disableSort:s}),{sortedItems:h}=cl(e,f,I,{transform:j=>({...j.raw,...j.columns}),sortFunctions:b,sortRawFunctions:y}),{flatItems:P}=hl(h,n,T),C=D(()=>P.value.length),{startIndex:A,stopIndex:K,pageCount:Q,setItemsPerPage:ne}=qa({page:c,itemsPerPage:d,itemsLength:C}),{paginatedItems:W}=Ya({items:P,startIndex:A,stopIndex:K,itemsPerPage:d}),re=D(()=>U(W.value)),{isSelected:de,select:w,selectAll:p,toggleSelect:_,someSelected:L,allSelected:$}=ol(e,{allItems:g,currentPage:re}),{isExpanded:F,toggleExpand:O}=Sl(e);Tl({page:c,itemsPerPage:d,sortBy:r,groupBy:n,search:k}),Ee({VDataTableRows:{hideNoData:H(()=>e.hideNoData),noDataText:H(()=>e.noDataText),loading:H(()=>e.loading),loadingText:H(()=>e.loadingText)}});const B=D(()=>({page:c.value,itemsPerPage:d.value,sortBy:r.value,pageCount:Q.value,toggleSort:V,setItemsPerPage:ne,someSelected:L.value,allSelected:$.value,isSelected:de,select:w,selectAll:p,toggleSelect:_,isExpanded:F,toggleExpand:O,isGroupOpen:R,toggleGroup:x,items:re.value.map(j=>j.raw),internalItems:re.value,groupedItems:W.value,columns:v.value,headers:m.value}));return ue(()=>{const j=ft.filterProps(e),G=vt.filterProps(e),ie=mt.filterProps(e),q=st.filterProps(e);return u(st,N({class:["v-data-table",{"v-data-table--show-select":e.showSelect,"v-data-table--loading":e.loading},e.class],style:e.style},q,{fixedHeader:e.fixedHeader||e.sticky}),{top:()=>{var se;return(se=a.top)==null?void 0:se.call(a,B.value)},default:()=>{var se,ye,ke,Pe,Qe,Ze;return a.default?a.default(B.value):u(ee,null,[(se=a.colgroup)==null?void 0:se.call(a,B.value),!e.hideDefaultHeader&&u("thead",{key:"thead"},[u(vt,G,a)]),(ye=a.thead)==null?void 0:ye.call(a,B.value),!e.hideDefaultBody&&u("tbody",null,[(ke=a["body.prepend"])==null?void 0:ke.call(a,B.value),a.body?a.body(B.value):u(mt,N(t,ie,{items:W.value}),a),(Pe=a["body.append"])==null?void 0:Pe.call(a,B.value)]),(Qe=a.tbody)==null?void 0:Qe.call(a,B.value),(Ze=a.tfoot)==null?void 0:Ze.call(a,B.value)])},bottom:()=>a.bottom?a.bottom(B.value):!e.hideDefaultFooter&&u(ee,null,[u(Da,null,null),u(ft,j,{prepend:a["footer.prepend"]})])})}),{}}}),Ll={class:"d-flex justify-space-between align-center mb-6"},Ol=ia({__name:"reports",setup(e){const l=z([]),t=z(!0),a=z(!1),n=z({month:new Date().getMonth()+1,year:new Date().getFullYear(),is_hidden:!1}),r=async()=>{try{t.value=!0;const d=await Oe.get("/api/report/list");l.value=d.data}catch(d){console.error("Error fetching reports:",d)}finally{t.value=!1}},i=async()=>{try{(await Oe.post("/api/report/addtoqueue",{...n.value,user_id:JSON.parse(localStorage.getItem("user")||"{}").id})).data.status==="success"&&(a.value=!1,r())}catch(d){console.error("Error generating report:",d)}},o=async d=>{try{const s=await Oe.post("/api/report/download",{filename:d},{responseType:"blob"}),v=window.URL.createObjectURL(new Blob([s.data])),m=document.createElement("a");m.href=v,m.setAttribute("download",d),document.body.appendChild(m),m.click(),m.remove()}catch(s){console.error("Error downloading report:",s)}},c=d=>{switch(d){case"completed":return"success";case"processing":return"warning";case"queue":return"info";case"failed":return"error";default:return"default"}};return ht(()=>{r()}),(d,s)=>(lt(),ca("div",null,[Te("div",Ll,[s[7]||(s[7]=Te("div",null,[Te("h1",{class:"text-h4 font-weight-bold mb-1"}," SLA Reports "),Te("p",{class:"text-body-1 mb-0"}," Generate and manage SLA performance reports ")],-1)),u(Y,{color:"primary",onClick:s[0]||(s[0]=v=>a.value=!0)},{default:M(()=>[u(ce,{start:"",icon:"tabler-plus"}),s[6]||(s[6]=te(" Generate Report "))]),_:1,__:[6]})]),u(nt,null,{default:M(()=>[u(rt,null,{default:M(()=>s[8]||(s[8]=[te("Generated Reports")])),_:1,__:[8]}),u(ot,null,{default:M(()=>[u(Rl,{items:l.value,loading:t.value,headers:[{title:"Report Name",key:"name"},{title:"Status",key:"status"},{title:"Created By",key:"created_by"},{title:"Created At",key:"created_at"},{title:"Actions",key:"actions",sortable:!1}]},{"item.status":M(({item:v})=>[u(Je,{color:c(v.status),size:"small"},{default:M(()=>[te(Ke(v.status),1)]),_:2},1032,["color"])]),"item.created_at":M(({item:v})=>[te(Ke(new Date(v.created_at).toLocaleDateString()),1)]),"item.actions":M(({item:v})=>[v.status==="completed"&&v.file_name?(lt(),da(Y,{key:0,icon:"",size:"small",color:"primary",onClick:m=>o(v.file_name)},{default:M(()=>[u(ce,{icon:"tabler-download"})]),_:2},1032,["onClick"])):fa("",!0),u(Y,{icon:"",size:"small",color:"info",onClick:m=>d.viewLog(v.id)},{default:M(()=>[u(ce,{icon:"tabler-eye"})]),_:2},1032,["onClick"])]),_:1},8,["items","loading"])]),_:1})]),_:1}),u(ma,{modelValue:a.value,"onUpdate:modelValue":s[5]||(s[5]=v=>a.value=v),"max-width":"500"},{default:M(()=>[u(nt,null,{default:M(()=>[u(rt,null,{default:M(()=>s[9]||(s[9]=[te("Generate New SLA Report")])),_:1,__:[9]}),u(ot,null,{default:M(()=>[u(Va,{onSubmit:Se(i,["prevent"])},{default:M(()=>[u(Ca,null,{default:M(()=>[u(Me,{cols:"6"},{default:M(()=>[u(Ye,{modelValue:n.value.month,"onUpdate:modelValue":s[1]||(s[1]=v=>n.value.month=v),items:[{title:"January",value:1},{title:"February",value:2},{title:"March",value:3},{title:"April",value:4},{title:"May",value:5},{title:"June",value:6},{title:"July",value:7},{title:"August",value:8},{title:"September",value:9},{title:"October",value:10},{title:"November",value:11},{title:"December",value:12}],label:"Month",required:""},null,8,["modelValue"])]),_:1}),u(Me,{cols:"6"},{default:M(()=>[u(je,{modelValue:n.value.year,"onUpdate:modelValue":s[2]||(s[2]=v=>n.value.year=v),label:"Year",type:"number",min:2020,max:new Date().getFullYear(),required:""},null,8,["modelValue","max"])]),_:1}),u(Me,{cols:"12"},{default:M(()=>[u(Ia,{modelValue:n.value.is_hidden,"onUpdate:modelValue":s[3]||(s[3]=v=>n.value.is_hidden=v),label:"Hidden Report (Admin Only)"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1}),u(va,null,{default:M(()=>[u(Fa),u(Y,{color:"grey",onClick:s[4]||(s[4]=v=>a.value=!1)},{default:M(()=>s[10]||(s[10]=[te(" Cancel ")])),_:1,__:[10]}),u(Y,{color:"primary",onClick:i},{default:M(()=>s[11]||(s[11]=[te(" Generate ")])),_:1,__:[11]})]),_:1})]),_:1})]),_:1},8,["modelValue"])]))}});typeof it=="function"&&it(Ol);export{Ol as default};
