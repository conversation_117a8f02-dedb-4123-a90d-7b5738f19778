import{u as y,m as L,a as w,V as v}from"./VOverlay-zhJGflkj.js";import{f as D}from"./forwardRefs-B931MWyl.js";import{a8 as A,a9 as B,aa as F,r as S,bm as I,b1 as R,w as g,ac as T,bC as C,p as d,b as E,aX as O,aL as W}from"./main-CWjS3hwz.js";const H=B({fullscreen:Boolean,retainFocus:{type:Boolean,default:!0},scrollable:Boolean,...L({origin:"center center",scrollStrategy:"block",transition:{component:w},zIndex:2400})},"VDialog"),z=A()({name:"VDialog",props:H(),emits:{"update:modelValue":e=>!0,afterEnter:()=>!0,afterLeave:()=>!0},setup(e,h){let{emit:m,slots:s}=h;const u=F(e,"modelValue"),{scopeId:V}=y(),t=S();function f(a){var l,c;const o=a.relatedTarget,r=a.target;if(o!==r&&((l=t.value)!=null&&l.contentEl)&&((c=t.value)!=null&&c.globalTop)&&![document,t.value.contentEl].includes(r)&&!t.value.contentEl.contains(r)){const n=C(t.value.contentEl);if(!n.length)return;const i=n[0],x=n[n.length-1];o===i?x.focus():i.focus()}}I(()=>{document.removeEventListener("focusin",f)}),R&&g(()=>u.value&&e.retainFocus,a=>{a?document.addEventListener("focusin",f):document.removeEventListener("focusin",f)},{immediate:!0});function b(){var a;m("afterEnter"),(e.scrim||e.retainFocus)&&((a=t.value)!=null&&a.contentEl)&&!t.value.contentEl.contains(document.activeElement)&&t.value.contentEl.focus({preventScroll:!0})}function P(){m("afterLeave")}return g(u,async a=>{var o;a||(await O(),(o=t.value.activatorEl)==null||o.focus({preventScroll:!0}))}),T(()=>{const a=v.filterProps(e),o=d({"aria-haspopup":"dialog"},e.activatorProps),r=d({tabindex:-1},e.contentProps);return E(v,d({ref:t,class:["v-dialog",{"v-dialog--fullscreen":e.fullscreen,"v-dialog--scrollable":e.scrollable},e.class],style:e.style},a,{modelValue:u.value,"onUpdate:modelValue":l=>u.value=l,"aria-modal":"true",activatorProps:o,contentProps:r,height:e.fullscreen?void 0:e.height,width:e.fullscreen?void 0:e.width,maxHeight:e.fullscreen?void 0:e.maxHeight,maxWidth:e.fullscreen?void 0:e.maxWidth,role:"dialog",onAfterEnter:b,onAfterLeave:P},V),{activator:s.activator,default:function(){for(var l=arguments.length,c=new Array(l),n=0;n<l;n++)c[n]=arguments[n];return E(W,{root:"VDialog"},{default:()=>{var i;return[(i=s.default)==null?void 0:i.call(s,...c)]}})}})}),D({},t)}});export{z as V};
