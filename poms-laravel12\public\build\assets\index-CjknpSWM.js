import{d as h,r as y,D as w,c as i,o as r,e,b as t,f as a,s as u,X as v,a0 as k,ax as g,F as S,i as x,g as V,t as s}from"./main-CWjS3hwz.js";import{a as d,V as m}from"./VRow-BqKZbSVo.js";import{V as c,b as _}from"./VCard-y1UgCbHQ.js";import{V as f}from"./VCardText-DnBTLtJ0.js";import{V as P}from"./VAvatar-Ds2UYUGl.js";import{V as D}from"./VTable-6Q--EYaT.js";import{V as T}from"./VChip-DuBqIAai.js";/* empty css              */import"./VImg-B7Lnz0wk.js";const I={class:"d-flex justify-space-between align-center mb-6"},E={key:0,class:"text-center py-12"},O={key:1},A={class:"d-flex justify-space-between"},j={class:"text-body-2 mb-1"},B={class:"text-h4 font-weight-bold"},L={class:"d-flex justify-space-between mb-3"},M={class:"d-flex justify-space-between mb-3"},R={class:"d-flex justify-space-between mb-3"},z={class:"d-flex justify-space-between"},N={class:"text-center"},F={class:"text-h4 font-weight-bold"},Z=h({__name:"index",setup(H){const o=y({crmStats:[{label:"IT Coordinator Pending Ack (15min)",count:0,color:"warning",icon:"tabler-clock"},{label:"IT Coordinator Exceed (15min)",count:0,color:"error",icon:"tabler-alert-triangle"},{label:"IT Specialist Pending Ack (4hr)",count:0,color:"info",icon:"tabler-user-clock"},{label:"IT Specialist Exceed (4hr)",count:0,color:"error",icon:"tabler-alert-circle"}],callCenterStats:{call_offered:0,call_handled:0,call_abandoned:0,answer_rate:0,service_level:0},performanceData:[],loading:!0}),b=async()=>{try{o.value.loading=!0,setTimeout(()=>{o.value.crmStats=[{label:"IT Coordinator Pending Ack (15min)",count:12,color:"warning",icon:"tabler-clock"},{label:"IT Coordinator Exceed (15min)",count:3,color:"error",icon:"tabler-alert-triangle"},{label:"IT Specialist Pending Ack (4hr)",count:8,color:"info",icon:"tabler-user-clock"},{label:"IT Specialist Exceed (4hr)",count:2,color:"error",icon:"tabler-alert-circle"}],o.value.callCenterStats={call_offered:245,call_handled:231,call_abandoned:14,answer_rate:94.3,service_level:92.1},o.value.performanceData=[{id:1,proxy_name:"EP Portal",response_time:120,status:"up",checked_at:new Date().toISOString()},{id:2,proxy_name:"EP Database",response_time:85,status:"up",checked_at:new Date().toISOString()},{id:3,proxy_name:"EP BPM",response_time:200,status:"up",checked_at:new Date().toISOString()},{id:4,proxy_name:"EP SSO",response_time:95,status:"up",checked_at:new Date().toISOString()}],o.value.loading=!1},1e3)}catch(p){console.error("Error fetching dashboard data:",p),o.value.loading=!1}};return w(()=>{b()}),(p,l)=>(r(),i("div",null,[e("div",I,[l[1]||(l[1]=e("div",null,[e("h1",{class:"text-h4 font-weight-bold mb-1"}," eP-POMS Dashboard "),e("p",{class:"text-body-1 mb-0"}," Performance Operations Monitoring System ")],-1)),t(k,{color:"primary",onClick:b,loading:o.value.loading},{default:a(()=>[t(v,{start:"",icon:"tabler-refresh"}),l[0]||(l[0]=u(" Refresh "))]),_:1,__:[0]},8,["loading"])]),o.value.loading?(r(),i("div",E,[t(g,{indeterminate:"",color:"primary",size:"64"}),l[2]||(l[2]=e("p",{class:"text-body-1 mt-4"},"Loading dashboard data...",-1))])):(r(),i("div",O,[t(m,{class:"mb-6"},{default:a(()=>[t(d,{cols:"12"},{default:a(()=>l[3]||(l[3]=[e("h2",{class:"text-h5 mb-4"},"CRM Incident Statistics",-1)])),_:1,__:[3]}),(r(!0),i(S,null,x(o.value.crmStats,(n,C)=>(r(),V(d,{cols:"12",md:"3",key:C},{default:a(()=>[t(c,null,{default:a(()=>[t(f,null,{default:a(()=>[e("div",A,[e("div",null,[e("p",j,s(n.label),1),e("h3",B,s(n.count),1)]),t(P,{color:n.color,variant:"tonal",size:"40"},{default:a(()=>[t(v,{icon:n.icon},null,8,["icon"])]),_:2},1032,["color"])])]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1}),t(m,{class:"mb-6"},{default:a(()=>[t(d,{cols:"12"},{default:a(()=>l[4]||(l[4]=[e("h2",{class:"text-h5 mb-4"},"Call Center Performance",-1)])),_:1,__:[4]}),t(d,{cols:"12",md:"6"},{default:a(()=>[t(c,null,{default:a(()=>[t(_,null,{default:a(()=>l[5]||(l[5]=[u("Today's Call Statistics")])),_:1,__:[5]}),t(f,null,{default:a(()=>[e("div",L,[l[6]||(l[6]=e("span",null,"Calls Offered:",-1)),e("strong",null,s(o.value.callCenterStats.call_offered),1)]),e("div",M,[l[7]||(l[7]=e("span",null,"Calls Handled:",-1)),e("strong",null,s(o.value.callCenterStats.call_handled),1)]),e("div",R,[l[8]||(l[8]=e("span",null,"Calls Abandoned:",-1)),e("strong",null,s(o.value.callCenterStats.call_abandoned),1)]),e("div",z,[l[9]||(l[9]=e("span",null,"Answer Rate:",-1)),e("strong",null,s(o.value.callCenterStats.answer_rate)+"%",1)])]),_:1})]),_:1})]),_:1}),t(d,{cols:"12",md:"6"},{default:a(()=>[t(c,null,{default:a(()=>[t(_,null,{default:a(()=>l[10]||(l[10]=[u("Service Level")])),_:1,__:[10]}),t(f,null,{default:a(()=>[e("div",N,[t(g,{"model-value":o.value.callCenterStats.service_level,size:120,width:8,color:"primary"},{default:a(()=>[e("span",F,s(o.value.callCenterStats.service_level)+"% ",1)]),_:1},8,["model-value"]),l[11]||(l[11]=e("p",{class:"text-body-2 mt-3"},"Current Service Level",-1))])]),_:1})]),_:1})]),_:1})]),_:1}),t(m,null,{default:a(()=>[t(d,{cols:"12"},{default:a(()=>l[12]||(l[12]=[e("h2",{class:"text-h5 mb-4"},"System Performance",-1)])),_:1,__:[12]}),t(d,{cols:"12"},{default:a(()=>[t(c,null,{default:a(()=>[t(_,null,{default:a(()=>l[13]||(l[13]=[u("EP Performance Monitoring")])),_:1,__:[13]}),t(f,null,{default:a(()=>[t(D,null,{default:a(()=>[l[14]||(l[14]=e("thead",null,[e("tr",null,[e("th",null,"Proxy Name"),e("th",null,"Response Time"),e("th",null,"Status"),e("th",null,"Last Checked")])],-1)),e("tbody",null,[(r(!0),i(S,null,x(o.value.performanceData,n=>(r(),i("tr",{key:n.id},[e("td",null,s(n.proxy_name),1),e("td",null,s(n.response_time)+"ms",1),e("td",null,[t(T,{color:n.status==="up"?"success":"error",size:"small"},{default:a(()=>[u(s(n.status),1)]),_:2},1032,["color"])]),e("td",null,s(new Date(n.checked_at).toLocaleString()),1)]))),128))])]),_:1,__:[14]})]),_:1})]),_:1})]),_:1})]),_:1})]))]))}});export{Z as default};
