import{d as R,V as w,ay as C,ab as Y,c as j,o as V,v as Q,g as E,l as M,b as t,m as s,as as P,at as I,az as Z,i as ee,f as n,y as ae,a8 as te,a9 as se,aa as le,W as S,aw as oe,aA as re,aB as ne,aC as ie,aD as de,al as ue,aE as ce,ah as me,aj as fe,ai as pe,aF as ve,an as ge,ae as be,ao as ye,aG as Ve,ap as he,aH as ke,aI as _e,aJ as xe,af as we,aq as Ce,aK as Pe,X as Ie,aL as L,a0 as N,p as Se,r as y,e as d,E as T,t as A,$ as Le,s as B,F as Te}from"./main-CWjS3hwz.js";import{V as Ae,a as Be}from"./VTextField-XbG62wJL.js";import{u as $,a as $e,b as Fe}from"./misc-mask-light-q5Fj9WwF.js";import{V as De}from"./VNodeRenderer-DunzcDYp.js";import{a as F}from"./index-Dq7h7Pqt.js";import{a as p,V as D}from"./VRow-BqKZbSVo.js";import{V as ze}from"./VImg-B7Lnz0wk.js";import{V as Re}from"./VCard-y1UgCbHQ.js";import{V as z}from"./VCardText-DnBTLtJ0.js";import{V as je,a as Ee}from"./VForm-CPID1Qyc.js";import{c as Me}from"./VAvatar-Ds2UYUGl.js";import"./forwardRefs-B931MWyl.js";/* empty css              */const Ne=R({name:"AppTextField",inheritAttrs:!1,__name:"AppTextField",setup(e){const o=w(()=>{const r=C().id,c=Y();return r?`app-text-field-${r}`:c}),u=w(()=>C().label);return(a,r)=>(V(),j("div",{class:Q(["app-text-field flex-grow-1",a.$attrs.class])},[s(u)?(V(),E(Ae,{key:0,for:s(o),class:"mb-1 text-body-2 text-wrap",style:{"line-height":"15px"},text:s(u)},null,8,["for","text"])):M("",!0),t(Be,P(I({...a.$attrs,class:null,label:void 0,variant:"outlined",id:s(o)})),Z({_:2},[ee(a.$slots,(c,f)=>({name:f,fn:n(v=>[ae(a.$slots,f,P(I(v||{})))])}))]),1040)],2))}}),qe="/build/assets/auth-v2-login-illustration-bordered-dark-cDkPk8mY.png",Oe="/build/assets/auth-v2-login-illustration-bordered-light-CIHqcIVA.png",Ue="/build/assets/auth-v2-login-illustration-dark-ClExSVqL.png",Ge="/build/assets/auth-v2-login-illustration-light-C4sKfRS1.png",He=Me("v-alert-title"),Je=["success","info","warning","error"],Ke=se({border:{type:[Boolean,String],validator:e=>typeof e=="boolean"||["top","end","bottom","start"].includes(e)},borderColor:String,closable:Boolean,closeIcon:{type:Ce,default:"$close"},closeLabel:{type:String,default:"$vuetify.close"},icon:{type:[Boolean,String,Function,Object],default:null},modelValue:{type:Boolean,default:!0},prominent:Boolean,title:String,text:String,type:{type:String,validator:e=>Je.includes(e)},...we(),...xe(),..._e(),...ke(),...he(),...Ve(),...ye(),...be(),...ge(),...ve({variant:"flat"})},"VAlert"),We=te()({name:"VAlert",props:Ke(),emits:{"click:close":e=>!0,"update:modelValue":e=>!0},setup(e,o){let{emit:u,slots:a}=o;const r=le(e,"modelValue"),c=S(()=>{if(e.icon!==!1)return e.type?e.icon??`$${e.type}`:e.icon}),{themeClasses:f}=oe(e),{colorClasses:v,colorStyles:g,variantClasses:l}=re(()=>({color:e.color??e.type,variant:e.variant})),{densityClasses:i}=ne(e),{dimensionStyles:m}=ie(e),{elevationClasses:q}=de(e),{locationStyles:O}=ue(e),{positionClasses:U}=ce(e),{roundedClasses:G}=me(e),{textColorClasses:H,textColorStyles:J}=fe(()=>e.borderColor),{t:K}=pe(),k=S(()=>({"aria-label":K(e.closeLabel),onClick(h){r.value=!1,u("click:close",h)}}));return()=>{const h=!!(a.prepend||c.value),W=!!(a.title||e.title),X=!!(a.close||e.closable);return r.value&&t(e.tag,{class:["v-alert",e.border&&{"v-alert--border":!!e.border,[`v-alert--border-${e.border===!0?"start":e.border}`]:!0},{"v-alert--prominent":e.prominent},f.value,v.value,i.value,q.value,U.value,G.value,l.value,e.class],style:[g.value,m.value,O.value,e.style],role:"alert"},{default:()=>{var _,x;return[Pe(!1,"v-alert"),e.border&&t("div",{key:"border",class:["v-alert__border",H.value],style:J.value},null),h&&t("div",{key:"prepend",class:"v-alert__prepend"},[a.prepend?t(L,{key:"prepend-defaults",disabled:!c.value,defaults:{VIcon:{density:e.density,icon:c.value,size:e.prominent?44:28}}},a.prepend):t(Ie,{key:"prepend-icon",density:e.density,icon:c.value,size:e.prominent?44:28},null)]),t("div",{class:"v-alert__content"},[W&&t(He,{key:"title"},{default:()=>{var b;return[((b=a.title)==null?void 0:b.call(a))??e.title]}}),((_=a.text)==null?void 0:_.call(a))??e.text,(x=a.default)==null?void 0:x.call(a)]),a.append&&t("div",{key:"append",class:"v-alert__append"},[a.append()]),X&&t("div",{key:"close",class:"v-alert__close"},[a.close?t(L,{key:"close-defaults",defaults:{VBtn:{icon:e.closeIcon,size:"x-small",variant:"text"}}},{default:()=>{var b;return[(b=a.close)==null?void 0:b.call(a,{props:k.value})]}}):t(N,Se({key:"close-btn",icon:e.closeIcon,size:"x-small",variant:"text"},k.value),null)])]}})}}}),Xe={href:"javascript:void(0)"},Ye={class:"auth-logo d-flex align-center gap-x-3"},Qe={class:"auth-title"},Ze={class:"position-relative bg-background w-100 me-0"},ea={class:"d-flex align-center justify-center w-100 h-100",style:{"padding-inline":"6.25rem"}},aa=["src"],ta={class:"d-flex align-center flex-wrap justify-space-between my-6"},ga=R({__name:"login",setup(e){const o=y({username:"",password:"",remember:!1}),u=y(!1),a=y(!1),r=y({}),c=async()=>{var g,l;try{a.value=!0,r.value={};const i=await F.post("/api/auth/login",{username:o.value.username,password:o.value.password});i.data.status==="success"&&(localStorage.setItem("auth_token",i.data.access_token),localStorage.setItem("user",JSON.stringify(i.data.user)),F.defaults.headers.common.Authorization=`Bearer ${i.data.access_token}`,window.location.href="/dashboard")}catch(i){((g=i.response)==null?void 0:g.status)===422?r.value=i.response.data.errors:((l=i.response)==null?void 0:l.status)===401?r.value={general:["Invalid username or password"]}:r.value={general:["An error occurred. Please try again."]}}finally{a.value=!1}},f=$(Ge,Ue,Oe,qe,!0),v=$(Fe,$e);return(g,l)=>{const i=Ne;return V(),j(Te,null,[d("a",Xe,[d("div",Ye,[t(s(De),{nodes:s(T).app.logo},null,8,["nodes"]),d("h1",Qe,A(s(T).app.title),1)])]),t(D,{"no-gutters":"",class:"auth-wrapper bg-surface"},{default:n(()=>[t(p,{md:"8",class:"d-none d-md-flex"},{default:n(()=>[d("div",Ze,[d("div",ea,[t(ze,{"max-width":"613",src:s(f),class:"auth-illustration mt-16 mb-2"},null,8,["src"])]),d("img",{class:"auth-footer-mask flip-in-rtl",src:s(v),alt:"auth-footer-mask",height:"280",width:"100"},null,8,aa)])]),_:1}),t(p,{cols:"12",md:"4",class:"auth-card-v2 d-flex align-center justify-center"},{default:n(()=>[t(Re,{flat:"","max-width":500,class:"mt-12 mt-sm-0 pa-6"},{default:n(()=>[t(z,null,{default:n(()=>l[4]||(l[4]=[d("h4",{class:"text-h4 mb-1"}," Welcome to eP-POMS! 👋🏻 ",-1),d("p",{class:"mb-0"}," Please sign-in to your account and start monitoring ",-1)])),_:1,__:[4]}),t(z,null,{default:n(()=>[t(je,{onSubmit:Le(c,["prevent"])},{default:n(()=>[t(D,null,{default:n(()=>[s(r).general?(V(),E(p,{key:0,cols:"12"},{default:n(()=>[t(We,{type:"error",class:"mb-4"},{default:n(()=>[B(A(s(r).general[0]),1)]),_:1})]),_:1})):M("",!0),t(p,{cols:"12"},{default:n(()=>[t(i,{modelValue:s(o).username,"onUpdate:modelValue":l[0]||(l[0]=m=>s(o).username=m),autofocus:"",label:"Username",type:"text",placeholder:"Enter your username","error-messages":s(r).username,disabled:s(a),required:""},null,8,["modelValue","error-messages","disabled"])]),_:1}),t(p,{cols:"12"},{default:n(()=>[t(i,{modelValue:s(o).password,"onUpdate:modelValue":l[1]||(l[1]=m=>s(o).password=m),label:"Password",placeholder:"············",type:s(u)?"text":"password",autocomplete:"password","append-inner-icon":s(u)?"tabler-eye-off":"tabler-eye","error-messages":s(r).password,disabled:s(a),required:"","onClick:appendInner":l[2]||(l[2]=m=>u.value=!s(u))},null,8,["modelValue","type","append-inner-icon","error-messages","disabled"]),d("div",ta,[t(Ee,{modelValue:s(o).remember,"onUpdate:modelValue":l[3]||(l[3]=m=>s(o).remember=m),label:"Remember me",disabled:s(a)},null,8,["modelValue","disabled"]),l[5]||(l[5]=d("a",{class:"text-primary",href:"javascript:void(0)"}," Forgot Password? ",-1))]),t(N,{block:"",type:"submit",loading:s(a),disabled:!s(o).username||!s(o).password},{default:n(()=>l[6]||(l[6]=[B(" Login ")])),_:1,__:[6]},8,["loading","disabled"])]),_:1}),t(p,{cols:"12",class:"text-body-2 text-center mt-4"},{default:n(()=>l[7]||(l[7]=[d("span",{class:"d-inline-block"}," Performance Operations Monitoring System ",-1)])),_:1,__:[7]})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})],64)}}});export{ga as default};
