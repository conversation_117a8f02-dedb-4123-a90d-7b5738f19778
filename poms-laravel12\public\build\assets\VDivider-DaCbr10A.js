import{a8 as c,a9 as u,aw as h,aj as m,V as g,b4 as s,ac as y,b as r,an as b,af as f}from"./main-CWjS3hwz.js";const _=u({color:String,inset:Boolean,length:[Number,String],opacity:[Number,String],thickness:[Number,String],vertical:Boolean,...f(),...b()},"VDivider"),V=c()({name:"VDivider",props:_(),setup(e,n){let{attrs:i,slots:a}=n;const{themeClasses:l}=h(e),{textColorClasses:o,textColorStyles:d}=m(()=>e.color),v=g(()=>{const t={};return e.length&&(t[e.vertical?"height":"width"]=s(e.length)),e.thickness&&(t[e.vertical?"borderRightWidth":"borderTopWidth"]=s(e.thickness)),t});return y(()=>{const t=r("hr",{class:[{"v-divider":!0,"v-divider--inset":e.inset,"v-divider--vertical":e.vertical},l.value,o.value,e.class],style:[v.value,d.value,{"--v-border-opacity":e.opacity},e.style],"aria-orientation":!i.role||i.role==="separator"?e.vertical?"vertical":"horizontal":void 0,role:`${i.role||"separator"}`},null);return a.default?r("div",{class:["v-divider__wrapper",{"v-divider__wrapper--vertical":e.vertical,"v-divider__wrapper--inset":e.inset}]},[t,r("div",{class:"v-divider__content"},[a.default()]),t]):t}),{}}});export{V};
