<script setup lang="ts">
import { ref, onMounted } from 'vue'
import axios from 'axios'

// Dashboard data
const dashboardData = ref({
  crmStats: [
    { label: 'IT Coordinator Pending Ack (15min)', count: 0, color: 'warning', icon: 'tabler-clock' },
    { label: 'IT Coordinator Exceed (15min)', count: 0, color: 'error', icon: 'tabler-alert-triangle' },
    { label: 'IT Specialist Pending Ack (4hr)', count: 0, color: 'info', icon: 'tabler-user-clock' },
    { label: 'IT Specialist Exceed (4hr)', count: 0, color: 'error', icon: 'tabler-alert-circle' }
  ],
  callCenterStats: {
    call_offered: 0,
    call_handled: 0,
    call_abandoned: 0,
    answer_rate: 0,
    service_level: 0
  },
  performanceData: [],
  loading: true
})

// Mock data for demonstration (replace with actual API calls)
const fetchDashboardData = async () => {
  try {
    dashboardData.value.loading = true

    // Simulate API calls with mock data
    setTimeout(() => {
      dashboardData.value.crmStats = [
        { label: 'IT Coordinator Pending Ack (15min)', count: 12, color: 'warning', icon: 'tabler-clock' },
        { label: 'IT Coordinator Exceed (15min)', count: 3, color: 'error', icon: 'tabler-alert-triangle' },
        { label: 'IT Specialist Pending Ack (4hr)', count: 8, color: 'info', icon: 'tabler-user-clock' },
        { label: 'IT Specialist Exceed (4hr)', count: 2, color: 'error', icon: 'tabler-alert-circle' }
      ]

      dashboardData.value.callCenterStats = {
        call_offered: 245,
        call_handled: 231,
        call_abandoned: 14,
        answer_rate: 94.3,
        service_level: 92.1
      }

      dashboardData.value.performanceData = [
        { id: 1, proxy_name: 'EP Portal', response_time: 120, status: 'up', checked_at: new Date().toISOString() },
        { id: 2, proxy_name: 'EP Database', response_time: 85, status: 'up', checked_at: new Date().toISOString() },
        { id: 3, proxy_name: 'EP BPM', response_time: 200, status: 'up', checked_at: new Date().toISOString() },
        { id: 4, proxy_name: 'EP SSO', response_time: 95, status: 'up', checked_at: new Date().toISOString() }
      ]

      dashboardData.value.loading = false
    }, 1000)

  } catch (error) {
    console.error('Error fetching dashboard data:', error)
    dashboardData.value.loading = false
  }
}

onMounted(() => {
  fetchDashboardData()
})
</script>

<template>
  <div>
    <!-- Page Header -->
    <div class="d-flex justify-space-between align-center mb-6">
      <div>
        <h1 class="text-h4 font-weight-bold mb-1">
          eP-POMS Dashboard
        </h1>
        <p class="text-body-1 mb-0">
          Performance Operations Monitoring System
        </p>
      </div>
      <VBtn
        color="primary"
        @click="fetchDashboardData"
        :loading="dashboardData.loading"
      >
        <VIcon start icon="tabler-refresh" />
        Refresh
      </VBtn>
    </div>

    <!-- Loading State -->
    <div v-if="dashboardData.loading" class="text-center py-12">
      <VProgressCircular
        indeterminate
        color="primary"
        size="64"
      />
      <p class="text-body-1 mt-4">Loading dashboard data...</p>
    </div>

    <!-- Dashboard Content -->
    <div v-else>
      <!-- CRM Statistics Cards -->
      <VRow class="mb-6">
        <VCol cols="12">
          <h2 class="text-h5 mb-4">CRM Incident Statistics</h2>
        </VCol>

        <VCol
          cols="12"
          md="3"
          v-for="(stat, index) in dashboardData.crmStats"
          :key="index"
        >
          <VCard>
            <VCardText>
              <div class="d-flex justify-space-between">
                <div>
                  <p class="text-body-2 mb-1">{{ stat.label }}</p>
                  <h3 class="text-h4 font-weight-bold">{{ stat.count }}</h3>
                </div>
                <VAvatar
                  :color="stat.color"
                  variant="tonal"
                  size="40"
                >
                  <VIcon :icon="stat.icon" />
                </VAvatar>
              </div>
            </VCardText>
          </VCard>
        </VCol>
      </VRow>

      <!-- Call Center Statistics -->
      <VRow class="mb-6">
        <VCol cols="12">
          <h2 class="text-h5 mb-4">Call Center Performance</h2>
        </VCol>

        <VCol cols="12" md="6">
          <VCard>
            <VCardTitle>Today's Call Statistics</VCardTitle>
            <VCardText>
              <div class="d-flex justify-space-between mb-3">
                <span>Calls Offered:</span>
                <strong>{{ dashboardData.callCenterStats.call_offered }}</strong>
              </div>
              <div class="d-flex justify-space-between mb-3">
                <span>Calls Handled:</span>
                <strong>{{ dashboardData.callCenterStats.call_handled }}</strong>
              </div>
              <div class="d-flex justify-space-between mb-3">
                <span>Calls Abandoned:</span>
                <strong>{{ dashboardData.callCenterStats.call_abandoned }}</strong>
              </div>
              <div class="d-flex justify-space-between">
                <span>Answer Rate:</span>
                <strong>{{ dashboardData.callCenterStats.answer_rate }}%</strong>
              </div>
            </VCardText>
          </VCard>
        </VCol>

        <VCol cols="12" md="6">
          <VCard>
            <VCardTitle>Service Level</VCardTitle>
            <VCardText>
              <div class="text-center">
                <VProgressCircular
                  :model-value="dashboardData.callCenterStats.service_level"
                  :size="120"
                  :width="8"
                  color="primary"
                >
                  <span class="text-h4 font-weight-bold">
                    {{ dashboardData.callCenterStats.service_level }}%
                  </span>
                </VProgressCircular>
                <p class="text-body-2 mt-3">Current Service Level</p>
              </div>
            </VCardText>
          </VCard>
        </VCol>
      </VRow>

      <!-- Performance Monitoring -->
      <VRow>
        <VCol cols="12">
          <h2 class="text-h5 mb-4">System Performance</h2>
        </VCol>

        <VCol cols="12">
          <VCard>
            <VCardTitle>EP Performance Monitoring</VCardTitle>
            <VCardText>
              <VTable>
                <thead>
                  <tr>
                    <th>Proxy Name</th>
                    <th>Response Time</th>
                    <th>Status</th>
                    <th>Last Checked</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="proxy in dashboardData.performanceData"
                    :key="proxy.id"
                  >
                    <td>{{ proxy.proxy_name }}</td>
                    <td>{{ proxy.response_time }}ms</td>
                    <td>
                      <VChip
                        :color="proxy.status === 'up' ? 'success' : 'error'"
                        size="small"
                      >
                        {{ proxy.status }}
                      </VChip>
                    </td>
                    <td>{{ new Date(proxy.checked_at).toLocaleString() }}</td>
                  </tr>
                </tbody>
              </VTable>
            </VCardText>
          </VCard>
        </VCol>
      </VRow>
    </div>
  </div>
</template>
