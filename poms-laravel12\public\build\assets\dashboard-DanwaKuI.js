import{a as b}from"./index-Dq7h7Pqt.js";import{d as k,r as D,D as P,c as u,e,b as a,f as l,a0 as R,ax as g,o as r,s as i,X as S,l as f,F as x,i as h,g as m,t as o}from"./main-CWjS3hwz.js";import{V as v,a as d}from"./VRow-BqKZbSVo.js";import{V as p,b as C}from"./VCard-y1UgCbHQ.js";import{V as _}from"./VCardText-DnBTLtJ0.js";import{V as j}from"./VAvatar-Ds2UYUGl.js";import{V as L}from"./VTable-6Q--EYaT.js";import{V as T}from"./VChip-DuBqIAai.js";import{b as V}from"./route-block-B_A1xBdJ.js";/* empty css              */import"./VImg-B7Lnz0wk.js";const B={class:"d-flex justify-space-between align-center mb-6"},M={key:0,class:"text-center py-12"},N={key:1},z={class:"d-flex justify-space-between"},A={class:"text-body-2 mb-1"},E={class:"text-h4 font-weight-bold"},O={class:"d-flex justify-space-between mb-3"},F={class:"d-flex justify-space-between mb-3"},I={class:"d-flex justify-space-between mb-3"},H={class:"d-flex justify-space-between"},X={class:"text-center"},q={class:"text-h4 font-weight-bold"},G=k({__name:"dashboard",setup(J){const s=D({crmStats:null,callCenterStats:null,performanceData:null,loading:!0}),y=async()=>{try{s.value.loading=!0;const c=await b.get("/api/crm/incident-stats");s.value.crmStats=c.data;const t=await b.get("/api/callcenter/dashboard");s.value.callCenterStats=t.data;const n=await b.get("/api/ep/performance");s.value.performanceData=n.data}catch(c){console.error("Error fetching dashboard data:",c)}finally{s.value.loading=!1}};return P(()=>{y()}),(c,t)=>(r(),u("div",null,[e("div",B,[t[1]||(t[1]=e("div",null,[e("h1",{class:"text-h4 font-weight-bold mb-1"}," eP-POMS Dashboard "),e("p",{class:"text-body-1 mb-0"}," Performance Operations Monitoring System ")],-1)),a(R,{color:"primary",onClick:y,loading:s.value.loading},{default:l(()=>[a(S,{start:"",icon:"tabler-refresh"}),t[0]||(t[0]=i(" Refresh "))]),_:1,__:[0]},8,["loading"])]),s.value.loading?(r(),u("div",M,[a(g,{indeterminate:"",color:"primary",size:"64"}),t[2]||(t[2]=e("p",{class:"text-body-1 mt-4"},"Loading dashboard data...",-1))])):(r(),u("div",N,[a(v,{class:"mb-6"},{default:l(()=>[a(d,{cols:"12"},{default:l(()=>t[3]||(t[3]=[e("h2",{class:"text-h5 mb-4"},"CRM Incident Statistics",-1)])),_:1,__:[3]}),s.value.crmStats?(r(!0),u(x,{key:0},h(s.value.crmStats,(n,w)=>(r(),m(d,{cols:"12",md:"3",key:w},{default:l(()=>[a(p,null,{default:l(()=>[a(_,null,{default:l(()=>[e("div",z,[e("div",null,[e("p",A,o(n.label),1),e("h3",E,o(n.count),1)]),a(j,{color:n.color||"primary",variant:"tonal",size:"40"},{default:l(()=>[a(S,{icon:n.icon||"tabler-chart-bar"},null,8,["icon"])]),_:2},1032,["color"])])]),_:2},1024)]),_:2},1024)]),_:2},1024))),128)):f("",!0)]),_:1}),a(v,{class:"mb-6"},{default:l(()=>[a(d,{cols:"12"},{default:l(()=>t[4]||(t[4]=[e("h2",{class:"text-h5 mb-4"},"Call Center Performance",-1)])),_:1,__:[4]}),a(d,{cols:"12",md:"6"},{default:l(()=>[a(p,null,{default:l(()=>[a(C,null,{default:l(()=>t[5]||(t[5]=[i("Today's Call Statistics")])),_:1,__:[5]}),s.value.callCenterStats?(r(),m(_,{key:0},{default:l(()=>[e("div",O,[t[6]||(t[6]=e("span",null,"Calls Offered:",-1)),e("strong",null,o(s.value.callCenterStats.call_offered||0),1)]),e("div",F,[t[7]||(t[7]=e("span",null,"Calls Handled:",-1)),e("strong",null,o(s.value.callCenterStats.call_handled||0),1)]),e("div",I,[t[8]||(t[8]=e("span",null,"Calls Abandoned:",-1)),e("strong",null,o(s.value.callCenterStats.call_abandoned||0),1)]),e("div",H,[t[9]||(t[9]=e("span",null,"Answer Rate:",-1)),e("strong",null,o(s.value.callCenterStats.answer_rate||0)+"%",1)])]),_:1})):f("",!0)]),_:1})]),_:1}),a(d,{cols:"12",md:"6"},{default:l(()=>[a(p,null,{default:l(()=>[a(C,null,{default:l(()=>t[10]||(t[10]=[i("Service Level")])),_:1,__:[10]}),s.value.callCenterStats?(r(),m(_,{key:0},{default:l(()=>[e("div",X,[a(g,{"model-value":s.value.callCenterStats.service_level||0,size:120,width:8,color:"primary"},{default:l(()=>[e("span",q,o(s.value.callCenterStats.service_level||0)+"% ",1)]),_:1},8,["model-value"]),t[11]||(t[11]=e("p",{class:"text-body-2 mt-3"},"Current Service Level",-1))])]),_:1})):f("",!0)]),_:1})]),_:1})]),_:1}),a(v,null,{default:l(()=>[a(d,{cols:"12"},{default:l(()=>t[12]||(t[12]=[e("h2",{class:"text-h5 mb-4"},"System Performance",-1)])),_:1,__:[12]}),a(d,{cols:"12"},{default:l(()=>[a(p,null,{default:l(()=>[a(C,null,{default:l(()=>t[13]||(t[13]=[i("EP Performance Monitoring")])),_:1,__:[13]}),s.value.performanceData?(r(),m(_,{key:0},{default:l(()=>[a(L,null,{default:l(()=>[t[14]||(t[14]=e("thead",null,[e("tr",null,[e("th",null,"Proxy Name"),e("th",null,"Response Time"),e("th",null,"Status"),e("th",null,"Last Checked")])],-1)),e("tbody",null,[(r(!0),u(x,null,h(s.value.performanceData,n=>(r(),u("tr",{key:n.id},[e("td",null,o(n.proxy_name),1),e("td",null,o(n.response_time)+"ms",1),e("td",null,[a(T,{color:n.status==="up"?"success":"error",size:"small"},{default:l(()=>[i(o(n.status),1)]),_:2},1032,["color"])]),e("td",null,o(new Date(n.checked_at).toLocaleString()),1)]))),128))])]),_:1,__:[14]})]),_:1})):f("",!0)]),_:1})]),_:1})]),_:1})]))]))}});typeof V=="function"&&V(G);export{G as default};
