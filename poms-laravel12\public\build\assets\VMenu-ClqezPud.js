import{u as T,b as w,V as E,m as I,a as M}from"./VOverlay-zhJGflkj.js";import{f as S}from"./forwardRefs-B931MWyl.js";import{a8 as O,a9 as K,aa as B,aO as N,ab as U,W as F,r as H,N as W,aQ as j,bm as z,bB as Q,aX as X,bC as V,w as $,V as q,p as A,ac as G,b as k,ad as J,bA as g,bD as Y,b1 as P,aL as Z,L as _,bE as ee}from"./main-CWjS3hwz.js";const te=K({id:String,submenu:Boolean,...J(I({closeDelay:250,closeOnContentClick:!0,locationStrategy:"connected",location:void 0,openDelay:300,scrim:!1,scrollStrategy:"reposition",transition:{component:M}}),["absolute"])},"VMenu"),se=O()({name:"VMenu",props:te(),emits:{"update:modelValue":a=>!0},setup(a,h){let{slots:c}=h;const s=B(a,"modelValue"),{scopeId:p}=T(),{isRtl:f}=N(),v=U(),m=F(()=>a.id||`v-menu-${v}`),o=H(),n=W(w,null),d=j(new Set);_(w,{register(){d.value.add(v)},unregister(){d.value.delete(v)},closeParents(e){setTimeout(()=>{var t;!d.value.size&&!a.persistent&&(e==null||(t=o.value)!=null&&t.contentEl&&!ee(e,o.value.contentEl))&&(s.value=!1,n==null||n.closeParents())},40)}}),z(()=>{n==null||n.unregister(),document.removeEventListener("focusin",b)}),Q(()=>s.value=!1);async function b(e){var r,u,i;const t=e.relatedTarget,l=e.target;await X(),s.value&&t!==l&&((r=o.value)!=null&&r.contentEl)&&((u=o.value)!=null&&u.globalTop)&&![document,o.value.contentEl].includes(l)&&!o.value.contentEl.contains(l)&&((i=V(o.value.contentEl)[0])==null||i.focus())}$(s,e=>{e?(n==null||n.register(),P&&document.addEventListener("focusin",b,{once:!0})):(n==null||n.unregister(),P&&document.removeEventListener("focusin",b))},{immediate:!0});function C(e){n==null||n.closeParents(e)}function D(e){var t,l,r,u,i;if(!a.disabled)if(e.key==="Tab"||e.key==="Enter"&&!a.closeOnContentClick){if(e.key==="Enter"&&(e.target instanceof HTMLTextAreaElement||e.target instanceof HTMLInputElement&&e.target.closest("form")))return;e.key==="Enter"&&e.preventDefault(),Y(V((t=o.value)==null?void 0:t.contentEl,!1),e.shiftKey?"prev":"next",L=>L.tabIndex>=0)||(s.value=!1,(r=(l=o.value)==null?void 0:l.activatorEl)==null||r.focus())}else a.submenu&&e.key===(f.value?"ArrowRight":"ArrowLeft")&&(s.value=!1,(i=(u=o.value)==null?void 0:u.activatorEl)==null||i.focus())}function y(e){var l;if(a.disabled)return;const t=(l=o.value)==null?void 0:l.contentEl;t&&s.value?e.key==="ArrowDown"?(e.preventDefault(),e.stopImmediatePropagation(),g(t,"next")):e.key==="ArrowUp"?(e.preventDefault(),e.stopImmediatePropagation(),g(t,"prev")):a.submenu&&(e.key===(f.value?"ArrowRight":"ArrowLeft")?s.value=!1:e.key===(f.value?"ArrowLeft":"ArrowRight")&&(e.preventDefault(),g(t,"first"))):(a.submenu?e.key===(f.value?"ArrowLeft":"ArrowRight"):["ArrowDown","ArrowUp"].includes(e.key))&&(s.value=!0,e.preventDefault(),setTimeout(()=>setTimeout(()=>y(e))))}const R=q(()=>A({"aria-haspopup":"menu","aria-expanded":String(s.value),"aria-controls":m.value,onKeydown:y},a.activatorProps));return G(()=>{const e=E.filterProps(a);return k(E,A({ref:o,id:m.value,class:["v-menu",a.class],style:a.style},e,{modelValue:s.value,"onUpdate:modelValue":t=>s.value=t,absolute:!0,activatorProps:R.value,location:a.location??(a.submenu?"end":"bottom"),"onClick:outside":C,onKeydown:D},p),{activator:c.activator,default:function(){for(var t=arguments.length,l=new Array(t),r=0;r<t;r++)l[r]=arguments[r];return k(Z,{root:"VMenu"},{default:()=>{var u;return[(u=c.default)==null?void 0:u.call(c,...l)]}})}})}),S({id:m,ΨopenChildren:d},o)}});export{se as V};
