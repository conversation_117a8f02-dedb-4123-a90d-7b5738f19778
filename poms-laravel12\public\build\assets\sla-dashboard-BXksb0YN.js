import{V as l,b as s}from"./VCard-y1UgCbHQ.js";import{V as n}from"./VCardText-DnBTLtJ0.js";import{d as i,c as d,b as t,f as a,o as m,s as p,e as o}from"./main-CWjS3hwz.js";import{b as r}from"./route-block-B_A1xBdJ.js";import"./VAvatar-Ds2UYUGl.js";import"./VImg-B7Lnz0wk.js";const f=i({__name:"sla-dashboard",setup(u){return(_,e)=>(m(),d("div",null,[t(l,null,{default:a(()=>[t(s,null,{default:a(()=>e[0]||(e[0]=[p("SLA Dashboard")])),_:1,__:[0]}),t(n,null,{default:a(()=>e[1]||(e[1]=[o("p",null,"SLA Dashboard functionality will be implemented here.",-1),o("p",null,"This page will show Service Level Agreement monitoring and statistics.",-1)])),_:1,__:[1]})]),_:1})]))}});typeof r=="function"&&r(f);export{f as default};
