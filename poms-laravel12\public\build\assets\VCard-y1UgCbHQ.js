import{a8 as o,af as v,aR as G,ac as m,b as n,a9 as p,ae as S,F as I,X as A,aL as k,t as P,aJ as B,aq as u,bs as J,aw as K,bu as X,aA as z,aB as Q,aC as U,aD as W,bf as Y,al as Z,aE as $,ah as ee,bt as ae,aF as te,an as ne,bx as de,ao as ie,aG as le,ap as se,bh as ce,aH as re,aI as ue,aV as oe,H as ve,bw as me,p as ye,aK as be,bg as fe}from"./main-CWjS3hwz.js";import{c as ge,V as h}from"./VAvatar-Ds2UYUGl.js";import{V as ke}from"./VCardText-DnBTLtJ0.js";import{V as pe}from"./VImg-B7Lnz0wk.js";const Ve=o()({name:"VCardActions",props:v(),setup(e,d){let{slots:t}=d;return G({VBtn:{slim:!0,variant:"text"}}),m(()=>{var a;return n("div",{class:["v-card-actions",e.class],style:e.style},[(a=t.default)==null?void 0:a.call(t)])}),{}}}),Ce=p({opacity:[Number,String],...v(),...S()},"VCardSubtitle"),Ie=o()({name:"VCardSubtitle",props:Ce(),setup(e,d){let{slots:t}=d;return m(()=>n(e.tag,{class:["v-card-subtitle",e.class],style:[{"--v-card-subtitle-opacity":e.opacity},e.style]},t)),{}}}),Ae=ge("v-card-title"),Pe=p({appendAvatar:String,appendIcon:u,prependAvatar:String,prependIcon:u,subtitle:{type:[String,Number,Boolean],default:void 0},title:{type:[String,Number,Boolean],default:void 0},...v(),...B()},"VCardItem"),he=o()({name:"VCardItem",props:Pe(),setup(e,d){let{slots:t}=d;return m(()=>{var s;const a=!!(e.prependAvatar||e.prependIcon),y=!!(a||t.prepend),l=!!(e.appendAvatar||e.appendIcon),b=!!(l||t.append),f=!!(e.title!=null||t.title),g=!!(e.subtitle!=null||t.subtitle);return n("div",{class:["v-card-item",e.class],style:e.style},[y&&n("div",{key:"prepend",class:"v-card-item__prepend"},[t.prepend?n(k,{key:"prepend-defaults",disabled:!a,defaults:{VAvatar:{density:e.density,image:e.prependAvatar},VIcon:{density:e.density,icon:e.prependIcon}}},t.prepend):n(I,null,[e.prependAvatar&&n(h,{key:"prepend-avatar",density:e.density,image:e.prependAvatar},null),e.prependIcon&&n(A,{key:"prepend-icon",density:e.density,icon:e.prependIcon},null)])]),n("div",{class:"v-card-item__content"},[f&&n(Ae,{key:"title"},{default:()=>{var i;return[((i=t.title)==null?void 0:i.call(t))??P(e.title)]}}),g&&n(Ie,{key:"subtitle"},{default:()=>{var i;return[((i=t.subtitle)==null?void 0:i.call(t))??P(e.subtitle)]}}),(s=t.default)==null?void 0:s.call(t)]),b&&n("div",{key:"append",class:"v-card-item__append"},[t.append?n(k,{key:"append-defaults",disabled:!l,defaults:{VAvatar:{density:e.density,image:e.appendAvatar},VIcon:{density:e.density,icon:e.appendIcon}}},t.append):n(I,null,[e.appendIcon&&n(A,{key:"append-icon",density:e.density,icon:e.appendIcon},null),e.appendAvatar&&n(h,{key:"append-avatar",density:e.density,image:e.appendAvatar},null)])])])}),{}}}),Se=p({appendAvatar:String,appendIcon:u,disabled:Boolean,flat:Boolean,hover:Boolean,image:String,link:{type:Boolean,default:void 0},prependAvatar:String,prependIcon:u,ripple:{type:[Boolean,Object],default:!0},subtitle:{type:[String,Number,Boolean],default:void 0},text:{type:[String,Number,Boolean],default:void 0},title:{type:[String,Number,Boolean],default:void 0},...oe(),...v(),...B(),...ue(),...re(),...ce(),...se(),...le(),...ie(),...de(),...S(),...ne(),...te({variant:"elevated"})},"VCard"),Te=o()({name:"VCard",directives:{Ripple:J},props:Se(),setup(e,d){let{attrs:t,slots:a}=d;const{themeClasses:y}=K(e),{borderClasses:l}=X(e),{colorClasses:b,colorStyles:f,variantClasses:g}=z(e),{densityClasses:s}=Q(e),{dimensionStyles:i}=U(e),{elevationClasses:x}=W(e),{loaderClasses:D}=Y(e),{locationStyles:L}=Z(e),{positionClasses:T}=$(e),{roundedClasses:_}=ee(e),c=ae(e,t);return m(()=>{const N=e.link!==!1&&c.isLink.value,r=!e.disabled&&e.link!==!1&&(e.link||c.isClickable.value),R=N?"a":e.tag,F=!!(a.title||e.title!=null),w=!!(a.subtitle||e.subtitle!=null),E=F||w,H=!!(a.append||e.appendAvatar||e.appendIcon),M=!!(a.prepend||e.prependAvatar||e.prependIcon),O=!!(a.image||e.image),j=E||M||H,q=!!(a.text||e.text!=null);return ve(n(R,ye({class:["v-card",{"v-card--disabled":e.disabled,"v-card--flat":e.flat,"v-card--hover":e.hover&&!(e.disabled||e.flat),"v-card--link":r},y.value,l.value,b.value,s.value,x.value,D.value,T.value,_.value,g.value,e.class],style:[f.value,i.value,L.value,e.style],onClick:r&&c.navigate,tabindex:e.disabled?-1:void 0},c.linkProps),{default:()=>{var V;return[O&&n("div",{key:"image",class:"v-card__image"},[a.image?n(k,{key:"image-defaults",disabled:!e.image,defaults:{VImg:{cover:!0,src:e.image}}},a.image):n(pe,{key:"image-img",cover:!0,src:e.image},null)]),n(fe,{name:"v-card",active:!!e.loading,color:typeof e.loading=="boolean"?void 0:e.loading},{default:a.loader}),j&&n(he,{key:"item",prependAvatar:e.prependAvatar,prependIcon:e.prependIcon,title:e.title,subtitle:e.subtitle,appendAvatar:e.appendAvatar,appendIcon:e.appendIcon},{default:a.item,prepend:a.prepend,title:a.title,subtitle:a.subtitle,append:a.append}),q&&n(ke,{key:"text"},{default:()=>{var C;return[((C=a.text)==null?void 0:C.call(a))??e.text]}}),(V=a.default)==null?void 0:V.call(a),a.actions&&n(Ve,null,{default:a.actions}),be(r,"v-card")]}}),[[me("ripple"),r&&e.ripple]])}),{}}});export{Te as V,he as a,Ae as b,Ve as c};
