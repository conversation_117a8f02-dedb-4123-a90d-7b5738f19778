import{a9 as Y,a8 as Z,bF as de,ae as ee,bi as Se,af as le,aq as z,aO as Ce,aP as xe,bG as ve,aQ as F,V as g,aS as te,bH as Ie,b1 as ae,w as Ve,ac as fe,b as i,bC as Pe,bI as ne,X as A,aF as he,an as pe,b6 as ze,aw as be,aR as Ae,W as V,p as X,bs as we,ai as Ee,bu as Re,aB as _e,aD as Fe,ah as Te,bJ as Be,aa as Ge,bK as Oe,bt as De,aA as Me,H as se,bw as Le,aU as We,bx as He,ao as Ke,bL as $e,aH as qe,aJ as Ne,aV as Ue,bj as ie,aK as je,bM as Je,aL as D,I as Qe,F as oe,t as Xe}from"./main-CWjS3hwz.js";import{V as ce}from"./VAvatar-Ds2UYUGl.js";function Ye(e){let{selectedElement:s,containerElement:a,isRtl:r,isHorizontal:o}=e;const b=T(o,a),u=ye(o,r,a),y=T(o,s),h=me(o,s),m=y*.4;return u>h?h-m:u+b<h+y?h-b+y+m:u}function Ze(e){let{selectedElement:s,containerElement:a,isHorizontal:r}=e;const o=T(r,a),b=me(r,s),u=T(r,s);return b-o/2+u/2}function ue(e,s){const a=e?"scrollWidth":"scrollHeight";return(s==null?void 0:s[a])||0}function el(e,s){const a=e?"clientWidth":"clientHeight";return(s==null?void 0:s[a])||0}function ye(e,s,a){if(!a)return 0;const{scrollLeft:r,offsetWidth:o,scrollWidth:b}=a;return e?s?b-o+r:r:a.scrollTop}function T(e,s){const a=e?"offsetWidth":"offsetHeight";return(s==null?void 0:s[a])||0}function me(e,s){const a=e?"offsetLeft":"offsetTop";return(s==null?void 0:s[a])||0}const ll=Symbol.for("vuetify:v-slide-group"),ke=Y({centerActive:Boolean,direction:{type:String,default:"horizontal"},symbol:{type:null,default:ll},nextIcon:{type:z,default:"$next"},prevIcon:{type:z,default:"$prev"},showArrows:{type:[Boolean,String],validator:e=>typeof e=="boolean"||["always","desktop","mobile"].includes(e)},...le(),...Se({mobile:null}),...ee(),...de({selectedClass:"v-slide-group-item--active"})},"VSlideGroup"),re=Z()({name:"VSlideGroup",props:ke(),emits:{"update:modelValue":e=>!0},setup(e,s){let{slots:a}=s;const{isRtl:r}=Ce(),{displayClasses:o,mobile:b}=xe(e),u=ve(e,e.symbol),y=F(!1),h=F(0),m=F(0),C=F(0),v=g(()=>e.direction==="horizontal"),{resizeRef:f,contentRect:n}=te(),{resizeRef:d,contentRect:w}=te(),k=Ie(),B=g(()=>({container:f.el,duration:200,easing:"easeOutQuart"})),M=g(()=>u.selected.value.length?u.items.value.findIndex(t=>t.id===u.selected.value[0]):-1),L=g(()=>u.selected.value.length?u.items.value.findIndex(t=>t.id===u.selected.value[u.selected.value.length-1]):-1);if(ae){let t=-1;Ve(()=>[u.selected.value,n.value,w.value,v.value],()=>{cancelAnimationFrame(t),t=requestAnimationFrame(()=>{if(n.value&&w.value){const l=v.value?"width":"height";m.value=n.value[l],C.value=w.value[l],y.value=m.value+1<C.value}if(M.value>=0&&d.el){const l=d.el.children[L.value];E(l,e.centerActive)}})})}const P=F(!1);function E(t,l){let c=0;l?c=Ze({containerElement:f.el,isHorizontal:v.value,selectedElement:t}):c=Ye({containerElement:f.el,isHorizontal:v.value,isRtl:r.value,selectedElement:t}),G(c)}function G(t){if(!ae||!f.el)return;const l=T(v.value,f.el),c=ye(v.value,r.value,f.el);if(!(ue(v.value,f.el)<=l||Math.abs(t-c)<16)){if(v.value&&r.value&&f.el){const{scrollWidth:J,offsetWidth:Q}=f.el;t=J-Q-t}v.value?k.horizontal(t,B.value):k(t,B.value)}}function p(t){const{scrollTop:l,scrollLeft:c}=t.target;h.value=v.value?c:l}function x(t){if(P.value=!0,!(!y.value||!d.el)){for(const l of t.composedPath())for(const c of d.el.children)if(c===l){E(c);return}}}function W(t){P.value=!1}let R=!1;function O(t){var l;!R&&!P.value&&!(t.relatedTarget&&((l=d.el)!=null&&l.contains(t.relatedTarget)))&&S(),R=!1}function _(){R=!0}function H(t){if(!d.el)return;function l(c){t.preventDefault(),S(c)}v.value?t.key==="ArrowRight"?l(r.value?"prev":"next"):t.key==="ArrowLeft"&&l(r.value?"next":"prev"):t.key==="ArrowDown"?l("next"):t.key==="ArrowUp"&&l("prev"),t.key==="Home"?l("first"):t.key==="End"&&l("last")}function I(t,l){if(!t)return;let c=t;do c=c==null?void 0:c[l==="next"?"nextElementSibling":"previousElementSibling"];while(c!=null&&c.hasAttribute("disabled"));return c}function S(t){if(!d.el)return;let l;if(!t)l=Pe(d.el)[0];else if(t==="next"){if(l=I(d.el.querySelector(":focus"),t),!l)return S("first")}else if(t==="prev"){if(l=I(d.el.querySelector(":focus"),t),!l)return S("last")}else t==="first"?(l=d.el.firstElementChild,l!=null&&l.hasAttribute("disabled")&&(l=I(l,"next"))):t==="last"&&(l=d.el.lastElementChild,l!=null&&l.hasAttribute("disabled")&&(l=I(l,"prev")));l&&l.focus({preventScroll:!0})}function K(t){const l=v.value&&r.value?-1:1,c=(t==="prev"?-l:l)*m.value;let j=h.value+c;if(v.value&&r.value&&f.el){const{scrollWidth:J,offsetWidth:Q}=f.el;j+=J-Q}G(j)}const $=g(()=>({next:u.next,prev:u.prev,select:u.select,isSelected:u.isSelected})),q=g(()=>{switch(e.showArrows){case"always":return!0;case"desktop":return!b.value;case!0:return y.value||Math.abs(h.value)>0;case"mobile":return b.value||y.value||Math.abs(h.value)>0;default:return!b.value&&(y.value||Math.abs(h.value)>0)}}),N=g(()=>Math.abs(h.value)>1),U=g(()=>{if(!f.value)return!1;const t=ue(v.value,f.el),l=el(v.value,f.el);return t-l-Math.abs(h.value)>1});return fe(()=>i(e.tag,{class:["v-slide-group",{"v-slide-group--vertical":!v.value,"v-slide-group--has-affixes":q.value,"v-slide-group--is-overflowing":y.value},o.value,e.class],style:e.style,tabindex:P.value||u.selected.value.length?-1:0,onFocus:O},{default:()=>{var t,l,c;return[q.value&&i("div",{key:"prev",class:["v-slide-group__prev",{"v-slide-group__prev--disabled":!N.value}],onMousedown:_,onClick:()=>N.value&&K("prev")},[((t=a.prev)==null?void 0:t.call(a,$.value))??i(ne,null,{default:()=>[i(A,{icon:r.value?e.nextIcon:e.prevIcon},null)]})]),i("div",{key:"container",ref:f,class:"v-slide-group__container",onScroll:p},[i("div",{ref:d,class:"v-slide-group__content",onFocusin:x,onFocusout:W,onKeydown:H},[(l=a.default)==null?void 0:l.call(a,$.value)])]),q.value&&i("div",{key:"next",class:["v-slide-group__next",{"v-slide-group__next--disabled":!U.value}],onMousedown:_,onClick:()=>U.value&&K("next")},[((c=a.next)==null?void 0:c.call(a,$.value))??i(ne,null,{default:()=>[i(A,{icon:r.value?e.prevIcon:e.nextIcon},null)]})])]}})),{selected:u.selected,scrollTo:K,scrollOffset:h,focus:S,hasPrev:N,hasNext:U}}}),ge=Symbol.for("vuetify:v-chip-group"),tl=Y({baseColor:String,column:Boolean,filter:Boolean,valueComparator:{type:Function,default:ze},...ke(),...le(),...de({selectedClass:"v-chip--selected"}),...ee(),...pe(),...he({variant:"tonal"})},"VChipGroup");Z()({name:"VChipGroup",props:tl(),emits:{"update:modelValue":e=>!0},setup(e,s){let{slots:a}=s;const{themeClasses:r}=be(e),{isSelected:o,select:b,next:u,prev:y,selected:h}=ve(e,ge);return Ae({VChip:{baseColor:V(()=>e.baseColor),color:V(()=>e.color),disabled:V(()=>e.disabled),filter:V(()=>e.filter),variant:V(()=>e.variant)}}),fe(()=>{const m=re.filterProps(e);return i(re,X(m,{class:["v-chip-group",{"v-chip-group--column":e.column},r.value,e.class],style:e.style}),{default:()=>{var C;return[(C=a.default)==null?void 0:C.call(a,{isSelected:o,select:b,next:u,prev:y,selected:h.value})]}})}),{}}});const al=Y({activeClass:String,appendAvatar:String,appendIcon:z,baseColor:String,closable:Boolean,closeIcon:{type:z,default:"$delete"},closeLabel:{type:String,default:"$vuetify.close"},draggable:Boolean,filter:Boolean,filterIcon:{type:z,default:"$complete"},label:Boolean,link:{type:Boolean,default:void 0},pill:Boolean,prependAvatar:String,prependIcon:z,ripple:{type:[Boolean,Object],default:!0},text:{type:[String,Number,Boolean],default:void 0},modelValue:{type:Boolean,default:!0},onClick:ie(),onClickOnce:ie(),...Ue(),...le(),...Ne(),...qe(),...$e(),...Ke(),...He(),...We(),...ee({tag:"span"}),...pe(),...he({variant:"tonal"})},"VChip"),il=Z()({name:"VChip",directives:{Ripple:we},props:al(),emits:{"click:close":e=>!0,"update:modelValue":e=>!0,"group:selected":e=>!0,click:e=>!0},setup(e,s){let{attrs:a,emit:r,slots:o}=s;const{t:b}=Ee(),{borderClasses:u}=Re(e),{densityClasses:y}=_e(e),{elevationClasses:h}=Fe(e),{roundedClasses:m}=Te(e),{sizeClasses:C}=Be(e),{themeClasses:v}=be(e),f=Ge(e,"modelValue"),n=Oe(e,ge,!1),d=De(e,a),w=V(()=>e.link!==!1&&d.isLink.value),k=g(()=>!e.disabled&&e.link!==!1&&(!!n||e.link||d.isClickable.value)),B=V(()=>({"aria-label":b(e.closeLabel),onClick(p){p.preventDefault(),p.stopPropagation(),f.value=!1,r("click:close",p)}})),{colorClasses:M,colorStyles:L,variantClasses:P}=Me(()=>({color:!n||n.isSelected.value?e.color??e.baseColor:e.baseColor,variant:e.variant}));function E(p){var x;r("click",p),k.value&&((x=d.navigate)==null||x.call(d,p),n==null||n.toggle())}function G(p){(p.key==="Enter"||p.key===" ")&&(p.preventDefault(),E(p))}return()=>{var I;const p=d.isLink.value?"a":e.tag,x=!!(e.appendIcon||e.appendAvatar),W=!!(x||o.append),R=!!(o.close||e.closable),O=!!(o.filter||e.filter)&&n,_=!!(e.prependIcon||e.prependAvatar),H=!!(_||o.prepend);return f.value&&se(i(p,X({class:["v-chip",{"v-chip--disabled":e.disabled,"v-chip--label":e.label,"v-chip--link":k.value,"v-chip--filter":O,"v-chip--pill":e.pill,[`${e.activeClass}`]:e.activeClass&&((I=d.isActive)==null?void 0:I.value)},v.value,u.value,M.value,y.value,h.value,m.value,C.value,P.value,n==null?void 0:n.selectedClass.value,e.class],style:[L.value,e.style],disabled:e.disabled||void 0,draggable:e.draggable,tabindex:k.value?0:void 0,onClick:E,onKeydown:k.value&&!w.value&&G},d.linkProps),{default:()=>{var S;return[je(k.value,"v-chip"),O&&i(Je,{key:"filter"},{default:()=>[se(i("div",{class:"v-chip__filter"},[o.filter?i(D,{key:"filter-defaults",disabled:!e.filterIcon,defaults:{VIcon:{icon:e.filterIcon}}},o.filter):i(A,{key:"filter-icon",icon:e.filterIcon},null)]),[[Qe,n.isSelected.value]])]}),H&&i("div",{key:"prepend",class:"v-chip__prepend"},[o.prepend?i(D,{key:"prepend-defaults",disabled:!_,defaults:{VAvatar:{image:e.prependAvatar,start:!0},VIcon:{icon:e.prependIcon,start:!0}}},o.prepend):i(oe,null,[e.prependIcon&&i(A,{key:"prepend-icon",icon:e.prependIcon,start:!0},null),e.prependAvatar&&i(ce,{key:"prepend-avatar",image:e.prependAvatar,start:!0},null)])]),i("div",{class:"v-chip__content","data-no-activator":""},[((S=o.default)==null?void 0:S.call(o,{isSelected:n==null?void 0:n.isSelected.value,selectedClass:n==null?void 0:n.selectedClass.value,select:n==null?void 0:n.select,toggle:n==null?void 0:n.toggle,value:n==null?void 0:n.value.value,disabled:e.disabled}))??Xe(e.text)]),W&&i("div",{key:"append",class:"v-chip__append"},[o.append?i(D,{key:"append-defaults",disabled:!x,defaults:{VAvatar:{end:!0,image:e.appendAvatar},VIcon:{end:!0,icon:e.appendIcon}}},o.append):i(oe,null,[e.appendIcon&&i(A,{key:"append-icon",end:!0,icon:e.appendIcon},null),e.appendAvatar&&i(ce,{key:"append-avatar",end:!0,image:e.appendAvatar},null)])]),R&&i("button",X({key:"close",class:"v-chip__close",type:"button","data-testid":"close-chip"},B.value),[o.close?i(D,{key:"close-defaults",defaults:{VIcon:{icon:e.closeIcon,size:"x-small"}}},o.close):i(A,{key:"close-icon",icon:e.closeIcon,size:"x-small"},null)])]}}),[[Le("ripple"),k.value&&e.ripple,null]])}}});export{il as V};
