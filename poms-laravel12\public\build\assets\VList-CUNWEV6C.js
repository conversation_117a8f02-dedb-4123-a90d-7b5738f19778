import{N as X,aQ as _,L as q,b5 as ge,bl as I,a9 as j,r as L,aa as ee,V as p,bm as be,b2 as at,W as k,bn as nt,bo as Se,D as lt,bp as it,a8 as N,ac as R,b as m,aL as te,bq as st,br as rt,H as he,I as ut,ae as U,af as $,aq as T,bs as ot,bt as ct,w as dt,aw as pe,bu as ke,aA as vt,aB as Ce,aC as Ie,aD as we,ah as Ae,bv as ft,bw as mt,p as ae,aF as Ve,an as Pe,bx as yt,ao as Le,aH as Oe,aI as Be,aJ as Te,aV as _e,bj as D,aK as gt,F as ce,X as de,t as ve,aj as bt,aY as St,by as je,b6 as ht,b0 as O,ad as Me,bz as xe,ag as pt,aR as kt,bA as Ct}from"./main-CWjS3hwz.js";import{M as It}from"./VImg-B7Lnz0wk.js";import{c as wt,V as fe}from"./VAvatar-Ds2UYUGl.js";import{V as At}from"./VDivider-DaCbr10A.js";const ne=Symbol.for("vuetify:list");function Fe(){const e=X(ne,{hasPrepend:_(!1),updateHasPrepend:()=>null}),l={hasPrepend:_(!1),updateHasPrepend:t=>{t&&(l.hasPrepend.value=t)}};return q(ne,l),e}function Ne(){return X(ne,null)}const ie=e=>{const l={activate:t=>{let{id:a,value:i,activated:n}=t;return a=I(a),e&&!i&&n.size===1&&n.has(a)||(i?n.add(a):n.delete(a)),n},in:(t,a,i)=>{let n=new Set;if(t!=null)for(const r of ge(t))n=l.activate({id:r,value:!0,activated:new Set(n),children:a,parents:i});return n},out:t=>Array.from(t)};return l},Ee=e=>{const l=ie(e);return{activate:a=>{let{activated:i,id:n,...r}=a;n=I(n);const u=i.has(n)?new Set([n]):new Set;return l.activate({...r,id:n,activated:u})},in:(a,i,n)=>{let r=new Set;if(a!=null){const u=ge(a);u.length&&(r=l.in(u.slice(0,1),i,n))}return r},out:(a,i,n)=>l.out(a,i,n)}},Vt=e=>{const l=ie(e);return{activate:a=>{let{id:i,activated:n,children:r,...u}=a;return i=I(i),r.has(i)?n:l.activate({id:i,activated:n,children:r,...u})},in:l.in,out:l.out}},Pt=e=>{const l=Ee(e);return{activate:a=>{let{id:i,activated:n,children:r,...u}=a;return i=I(i),r.has(i)?n:l.activate({id:i,activated:n,children:r,...u})},in:l.in,out:l.out}},Lt={open:e=>{let{id:l,value:t,opened:a,parents:i}=e;if(t){const n=new Set;n.add(l);let r=i.get(l);for(;r!=null;)n.add(r),r=i.get(r);return n}else return a.delete(l),a},select:()=>null},De={open:e=>{let{id:l,value:t,opened:a,parents:i}=e;if(t){let n=i.get(l);for(a.add(l);n!=null&&n!==l;)a.add(n),n=i.get(n);return a}else a.delete(l);return a},select:()=>null},Ot={open:De.open,select:e=>{let{id:l,value:t,opened:a,parents:i}=e;if(!t)return a;const n=[];let r=i.get(l);for(;r!=null;)n.push(r),r=i.get(r);return new Set(n)}},se=e=>{const l={select:t=>{let{id:a,value:i,selected:n}=t;if(a=I(a),e&&!i){const r=Array.from(n.entries()).reduce((u,d)=>{let[c,y]=d;return y==="on"&&u.push(c),u},[]);if(r.length===1&&r[0]===a)return n}return n.set(a,i?"on":"off"),n},in:(t,a,i)=>{const n=new Map;for(const r of t||[])l.select({id:r,value:!0,selected:n,children:a,parents:i});return n},out:t=>{const a=[];for(const[i,n]of t.entries())n==="on"&&a.push(i);return a}};return l},Ge=e=>{const l=se(e);return{select:a=>{let{selected:i,id:n,...r}=a;n=I(n);const u=i.has(n)?new Map([[n,i.get(n)]]):new Map;return l.select({...r,id:n,selected:u})},in:(a,i,n)=>a!=null&&a.length?l.in(a.slice(0,1),i,n):new Map,out:(a,i,n)=>l.out(a,i,n)}},Bt=e=>{const l=se(e);return{select:a=>{let{id:i,selected:n,children:r,...u}=a;return i=I(i),r.has(i)?n:l.select({id:i,selected:n,children:r,...u})},in:l.in,out:l.out}},Tt=e=>{const l=Ge(e);return{select:a=>{let{id:i,selected:n,children:r,...u}=a;return i=I(i),r.has(i)?n:l.select({id:i,selected:n,children:r,...u})},in:l.in,out:l.out}},Re=e=>{const l={select:t=>{let{id:a,value:i,selected:n,children:r,parents:u}=t;a=I(a);const d=new Map(n),c=[a];for(;c.length;){const f=c.shift();n.set(I(f),i?"on":"off"),r.has(f)&&c.push(...r.get(f))}let y=I(u.get(a));for(;y;){const f=r.get(y),h=f.every(s=>n.get(I(s))==="on"),b=f.every(s=>!n.has(I(s))||n.get(I(s))==="off");n.set(y,h?"on":b?"off":"indeterminate"),y=I(u.get(y))}return e&&!i&&Array.from(n.entries()).reduce((h,b)=>{let[s,o]=b;return o==="on"&&h.push(s),h},[]).length===0?d:n},in:(t,a,i)=>{let n=new Map;for(const r of t||[])n=l.select({id:r,value:!0,selected:n,children:a,parents:i});return n},out:(t,a)=>{const i=[];for(const[n,r]of t.entries())r==="on"&&!a.has(n)&&i.push(n);return i}};return l},_t=e=>{const l=Re(e);return{select:l.select,in:l.in,out:(a,i,n)=>{const r=[];for(const[u,d]of a.entries())if(d==="on"){if(n.has(u)){const c=n.get(u);if(a.get(c)==="on")continue}r.push(u)}return r}}},G=Symbol.for("vuetify:nested"),Ue={id:_(),root:{register:()=>null,unregister:()=>null,parents:L(new Map),children:L(new Map),open:()=>null,openOnSelect:()=>null,activate:()=>null,select:()=>null,activatable:L(!1),selectable:L(!1),opened:L(new Set),activated:L(new Set),selected:L(new Map),selectedValues:L([]),getPath:()=>[]}},jt=j({activatable:Boolean,selectable:Boolean,activeStrategy:[String,Function,Object],selectStrategy:[String,Function,Object],openStrategy:[String,Object],opened:null,activated:null,selected:null,mandatory:Boolean},"nested"),Mt=e=>{let l=!1;const t=L(new Map),a=L(new Map),i=ee(e,"opened",e.opened,s=>new Set(s),s=>[...s.values()]),n=p(()=>{if(typeof e.activeStrategy=="object")return e.activeStrategy;if(typeof e.activeStrategy=="function")return e.activeStrategy(e.mandatory);switch(e.activeStrategy){case"leaf":return Vt(e.mandatory);case"single-leaf":return Pt(e.mandatory);case"independent":return ie(e.mandatory);case"single-independent":default:return Ee(e.mandatory)}}),r=p(()=>{if(typeof e.selectStrategy=="object")return e.selectStrategy;if(typeof e.selectStrategy=="function")return e.selectStrategy(e.mandatory);switch(e.selectStrategy){case"single-leaf":return Tt(e.mandatory);case"leaf":return Bt(e.mandatory);case"independent":return se(e.mandatory);case"single-independent":return Ge(e.mandatory);case"trunk":return _t(e.mandatory);case"classic":default:return Re(e.mandatory)}}),u=p(()=>{if(typeof e.openStrategy=="object")return e.openStrategy;switch(e.openStrategy){case"list":return Ot;case"single":return Lt;case"multiple":default:return De}}),d=ee(e,"activated",e.activated,s=>n.value.in(s,t.value,a.value),s=>n.value.out(s,t.value,a.value)),c=ee(e,"selected",e.selected,s=>r.value.in(s,t.value,a.value),s=>r.value.out(s,t.value,a.value));be(()=>{l=!0});function y(s){const o=[];let v=s;for(;v!=null;)o.unshift(v),v=a.value.get(v);return o}const f=at("nested"),h=new Set,b={id:_(),root:{opened:i,activatable:k(()=>e.activatable),selectable:k(()=>e.selectable),activated:d,selected:c,selectedValues:p(()=>{const s=[];for(const[o,v]of c.value.entries())v==="on"&&s.push(o);return s}),register:(s,o,v)=>{if(h.has(s)){y(s).map(String).join(" -> "),y(o).concat(s).map(String).join(" -> ");return}else h.add(s);o&&s!==o&&a.value.set(s,o),v&&t.value.set(s,[]),o!=null&&t.value.set(o,[...t.value.get(o)||[],s])},unregister:s=>{if(l)return;h.delete(s),t.value.delete(s);const o=a.value.get(s);if(o){const v=t.value.get(o)??[];t.value.set(o,v.filter(g=>g!==s))}a.value.delete(s)},open:(s,o,v)=>{f.emit("click:open",{id:s,value:o,path:y(s),event:v});const g=u.value.open({id:s,value:o,opened:new Set(i.value),children:t.value,parents:a.value,event:v});g&&(i.value=g)},openOnSelect:(s,o,v)=>{const g=u.value.select({id:s,value:o,selected:new Map(c.value),opened:new Set(i.value),children:t.value,parents:a.value,event:v});g&&(i.value=g)},select:(s,o,v)=>{f.emit("click:select",{id:s,value:o,path:y(s),event:v});const g=r.value.select({id:s,value:o,selected:new Map(c.value),children:t.value,parents:a.value,event:v});g&&(c.value=g),b.root.openOnSelect(s,o,v)},activate:(s,o,v)=>{if(!e.activatable)return b.root.select(s,!0,v);f.emit("click:activate",{id:s,value:o,path:y(s),event:v});const g=n.value.activate({id:s,value:o,activated:new Set(d.value),children:t.value,parents:a.value,event:v});if(g.size!==d.value.size)d.value=g;else{for(const C of g)if(!d.value.has(C)){d.value=g;return}for(const C of d.value)if(!g.has(C)){d.value=g;return}}},children:t,parents:a,getPath:y}};return q(G,b),b.root},$e=(e,l)=>{const t=X(G,Ue),a=Symbol("nested item"),i=p(()=>nt(e)??a),n={...t,id:i,open:(r,u)=>t.root.open(i.value,r,u),openOnSelect:(r,u)=>t.root.openOnSelect(i.value,r,u),isOpen:p(()=>t.root.opened.value.has(i.value)),parent:p(()=>t.root.parents.value.get(i.value)),activate:(r,u)=>t.root.activate(i.value,r,u),isActivated:p(()=>t.root.activated.value.has(I(i.value))),select:(r,u)=>t.root.select(i.value,r,u),isSelected:p(()=>t.root.selected.value.get(I(i.value))==="on"),isIndeterminate:p(()=>t.root.selected.value.get(I(i.value))==="indeterminate"),isLeaf:p(()=>!t.root.children.value.get(i.value)),isGroupActivator:t.isGroupActivator};return Se(()=>{!t.isGroupActivator&&t.root.register(i.value,t.id.value,l)}),be(()=>{!t.isGroupActivator&&t.root.unregister(i.value)}),l&&q(G,n),n},xt=()=>{const e=X(G,Ue);q(G,{...e,isGroupActivator:!0})};function Ft(){const e=_(!1);return lt(()=>{window.requestAnimationFrame(()=>{e.value=!0})}),{ssrBootStyles:k(()=>e.value?void 0:{transition:"none !important"}),isBooted:it(e)}}const Nt=st({name:"VListGroupActivator",setup(e,l){let{slots:t}=l;return xt(),()=>{var a;return(a=t.default)==null?void 0:a.call(t)}}}),Et=j({activeColor:String,baseColor:String,color:String,collapseIcon:{type:T,default:"$collapse"},expandIcon:{type:T,default:"$expand"},prependIcon:T,appendIcon:T,fluid:Boolean,subgroup:Boolean,title:String,value:null,...$(),...U()},"VListGroup"),me=N()({name:"VListGroup",props:Et(),setup(e,l){let{slots:t}=l;const{isOpen:a,open:i,id:n}=$e(()=>e.value,!0),r=p(()=>`v-list-group--id-${String(n.value)}`),u=Ne(),{isBooted:d}=Ft();function c(b){var s;b.stopPropagation(),!["INPUT","TEXTAREA"].includes((s=b.target)==null?void 0:s.tagName)&&i(!a.value,b)}const y=p(()=>({onClick:c,class:"v-list-group__header",id:r.value})),f=p(()=>a.value?e.collapseIcon:e.expandIcon),h=p(()=>({VListItem:{active:a.value,activeColor:e.activeColor,baseColor:e.baseColor,color:e.color,prependIcon:e.prependIcon||e.subgroup&&f.value,appendIcon:e.appendIcon||!e.subgroup&&f.value,title:e.title,value:e.value}}));return R(()=>m(e.tag,{class:["v-list-group",{"v-list-group--prepend":u==null?void 0:u.hasPrepend.value,"v-list-group--fluid":e.fluid,"v-list-group--subgroup":e.subgroup,"v-list-group--open":a.value},e.class],style:e.style},{default:()=>[t.activator&&m(te,{defaults:h.value},{default:()=>[m(Nt,null,{default:()=>[t.activator({props:y.value,isOpen:a.value})]})]}),m(It,{transition:{component:rt},disabled:!d.value},{default:()=>{var b;return[he(m("div",{class:"v-list-group__items",role:"group","aria-labelledby":r.value},[(b=t.default)==null?void 0:b.call(t)]),[[ut,a.value]])]}})]})),{isOpen:a}}}),Dt=j({opacity:[Number,String],...$(),...U()},"VListItemSubtitle"),Gt=N()({name:"VListItemSubtitle",props:Dt(),setup(e,l){let{slots:t}=l;return R(()=>m(e.tag,{class:["v-list-item-subtitle",e.class],style:[{"--v-list-item-subtitle-opacity":e.opacity},e.style]},t)),{}}}),Rt=wt("v-list-item-title"),Ut=j({active:{type:Boolean,default:void 0},activeClass:String,activeColor:String,appendAvatar:String,appendIcon:T,baseColor:String,disabled:Boolean,lines:[Boolean,String],link:{type:Boolean,default:void 0},nav:Boolean,prependAvatar:String,prependIcon:T,ripple:{type:[Boolean,Object],default:!0},slim:Boolean,subtitle:{type:[String,Number,Boolean],default:void 0},title:{type:[String,Number,Boolean],default:void 0},value:null,onClick:D(),onClickOnce:D(),..._e(),...$(),...Te(),...Be(),...Oe(),...Le(),...yt(),...U(),...Pe(),...Ve({variant:"text"})},"VListItem"),ye=N()({name:"VListItem",directives:{Ripple:ot},props:Ut(),emits:{click:e=>!0},setup(e,l){let{attrs:t,slots:a,emit:i}=l;const n=ct(e,t),r=p(()=>e.value===void 0?n.href.value:e.value),{activate:u,isActivated:d,select:c,isOpen:y,isSelected:f,isIndeterminate:h,isGroupActivator:b,root:s,parent:o,openOnSelect:v,id:g}=$e(r,!1),C=Ne(),A=p(()=>{var S;return e.active!==!1&&(e.active||((S=n.isActive)==null?void 0:S.value)||(s.activatable.value?d.value:f.value))}),E=k(()=>e.link!==!1&&n.isLink.value),M=p(()=>!!C&&(s.selectable.value||s.activatable.value||e.value!=null)),V=p(()=>!e.disabled&&e.link!==!1&&(e.link||n.isClickable.value||M.value)),z=k(()=>e.rounded||e.nav),W=k(()=>e.color??e.activeColor),J=k(()=>({color:A.value?W.value??e.baseColor:e.baseColor,variant:e.variant}));dt(()=>{var S;return(S=n.isActive)==null?void 0:S.value},S=>{S&&H()}),Se(()=>{var S;(S=n.isActive)!=null&&S.value&&H()});function H(){o.value!=null&&s.open(o.value,!0),v(!0)}const{themeClasses:Q}=pe(e),{borderClasses:B}=ke(e),{colorClasses:w,colorStyles:x,variantClasses:qe}=vt(J),{densityClasses:ze}=Ce(e),{dimensionStyles:We}=Ie(e),{elevationClasses:Je}=we(e),{roundedClasses:Qe}=Ae(z),Ye=k(()=>e.lines?`v-list-item--${e.lines}-line`:void 0),Y=p(()=>({isActive:A.value,select:c,isOpen:y.value,isSelected:f.value,isIndeterminate:h.value}));function Ze(S){var F,K;i("click",S),!["INPUT","TEXTAREA"].includes((F=S.target)==null?void 0:F.tagName)&&V.value&&((K=n.navigate)==null||K.call(n,S),!b&&(s.activatable.value?u(!d.value,S):(s.selectable.value||e.value!=null)&&c(!f.value,S)))}function et(S){const F=S.target;["INPUT","TEXTAREA"].includes(F.tagName)||(S.key==="Enter"||S.key===" ")&&(S.preventDefault(),S.target.dispatchEvent(new MouseEvent("click",S)))}return R(()=>{const S=E.value?"a":e.tag,F=a.title||e.title!=null,K=a.subtitle||e.subtitle!=null,re=!!(e.appendAvatar||e.appendIcon),tt=!!(re||a.append),ue=!!(e.prependAvatar||e.prependIcon),Z=!!(ue||a.prepend);return C==null||C.updateHasPrepend(Z),e.activeColor&&ft("active-color",["color","base-color"]),he(m(S,ae({class:["v-list-item",{"v-list-item--active":A.value,"v-list-item--disabled":e.disabled,"v-list-item--link":V.value,"v-list-item--nav":e.nav,"v-list-item--prepend":!Z&&(C==null?void 0:C.hasPrepend.value),"v-list-item--slim":e.slim,[`${e.activeClass}`]:e.activeClass&&A.value},Q.value,B.value,w.value,ze.value,Je.value,Ye.value,Qe.value,qe.value,e.class],style:[x.value,We.value,e.style],tabindex:V.value?C?-2:0:void 0,"aria-selected":M.value?s.activatable.value?d.value:s.selectable.value?f.value:A.value:void 0,onClick:Ze,onKeydown:V.value&&!E.value&&et},n.linkProps),{default:()=>{var oe;return[gt(V.value||A.value,"v-list-item"),Z&&m("div",{key:"prepend",class:"v-list-item__prepend"},[a.prepend?m(te,{key:"prepend-defaults",disabled:!ue,defaults:{VAvatar:{density:e.density,image:e.prependAvatar},VIcon:{density:e.density,icon:e.prependIcon},VListItemAction:{start:!0}}},{default:()=>{var P;return[(P=a.prepend)==null?void 0:P.call(a,Y.value)]}}):m(ce,null,[e.prependAvatar&&m(fe,{key:"prepend-avatar",density:e.density,image:e.prependAvatar},null),e.prependIcon&&m(de,{key:"prepend-icon",density:e.density,icon:e.prependIcon},null)]),m("div",{class:"v-list-item__spacer"},null)]),m("div",{class:"v-list-item__content","data-no-activator":""},[F&&m(Rt,{key:"title"},{default:()=>{var P;return[((P=a.title)==null?void 0:P.call(a,{title:e.title}))??ve(e.title)]}}),K&&m(Gt,{key:"subtitle"},{default:()=>{var P;return[((P=a.subtitle)==null?void 0:P.call(a,{subtitle:e.subtitle}))??ve(e.subtitle)]}}),(oe=a.default)==null?void 0:oe.call(a,Y.value)]),tt&&m("div",{key:"append",class:"v-list-item__append"},[a.append?m(te,{key:"append-defaults",disabled:!re,defaults:{VAvatar:{density:e.density,image:e.appendAvatar},VIcon:{density:e.density,icon:e.appendIcon},VListItemAction:{end:!0}}},{default:()=>{var P;return[(P=a.append)==null?void 0:P.call(a,Y.value)]}}):m(ce,null,[e.appendIcon&&m(de,{key:"append-icon",density:e.density,icon:e.appendIcon},null),e.appendAvatar&&m(fe,{key:"append-avatar",density:e.density,image:e.appendAvatar},null)]),m("div",{class:"v-list-item__spacer"},null)])]}}),[[mt("ripple"),V.value&&e.ripple]])}),{activate:u,isActivated:d,isGroupActivator:b,isSelected:f,list:C,select:c,root:s,id:g,link:n}}}),$t=j({color:String,inset:Boolean,sticky:Boolean,title:String,...$(),...U()},"VListSubheader"),Ht=N()({name:"VListSubheader",props:$t(),setup(e,l){let{slots:t}=l;const{textColorClasses:a,textColorStyles:i}=bt(()=>e.color);return R(()=>{const n=!!(t.default||e.title);return m(e.tag,{class:["v-list-subheader",{"v-list-subheader--inset":e.inset,"v-list-subheader--sticky":e.sticky},a.value,e.class],style:[{textColorStyles:i},e.style]},{default:()=>{var r;return[n&&m("div",{class:"v-list-subheader__text"},[((r=t.default)==null?void 0:r.call(t))??e.title])]}})}),{}}}),Kt=j({items:Array,returnObject:Boolean},"VListChildren"),He=N()({name:"VListChildren",props:Kt(),setup(e,l){let{slots:t}=l;return Fe(),()=>{var a,i;return((a=t.default)==null?void 0:a.call(t))??((i=e.items)==null?void 0:i.map(n=>{var h,b;let{children:r,props:u,type:d,raw:c}=n;if(d==="divider")return((h=t.divider)==null?void 0:h.call(t,{props:u}))??m(At,u,null);if(d==="subheader")return((b=t.subheader)==null?void 0:b.call(t,{props:u}))??m(Ht,u,null);const y={subtitle:t.subtitle?s=>{var o;return(o=t.subtitle)==null?void 0:o.call(t,{...s,item:c})}:void 0,prepend:t.prepend?s=>{var o;return(o=t.prepend)==null?void 0:o.call(t,{...s,item:c})}:void 0,append:t.append?s=>{var o;return(o=t.append)==null?void 0:o.call(t,{...s,item:c})}:void 0,title:t.title?s=>{var o;return(o=t.title)==null?void 0:o.call(t,{...s,item:c})}:void 0},f=me.filterProps(u);return r?m(me,ae({value:u==null?void 0:u.value},f),{activator:s=>{let{props:o}=s;const v={...u,...o,value:e.returnObject?c:u.value};return t.header?t.header({props:v}):m(ye,v,y)},default:()=>m(He,{items:r,returnObject:e.returnObject},t)}):t.item?t.item({props:u}):m(ye,ae(u,{value:e.returnObject?c:u.value}),y)}))}}}),Xt=j({items:{type:Array,default:()=>[]},itemTitle:{type:[String,Array,Function],default:"title"},itemValue:{type:[String,Array,Function],default:"value"},itemChildren:{type:[Boolean,String,Array,Function],default:"children"},itemProps:{type:[Boolean,String,Array,Function],default:"props"},returnObject:Boolean,valueComparator:Function},"list-items");function le(e,l){const t=O(l,e.itemTitle,l),a=O(l,e.itemValue,t),i=O(l,e.itemChildren),n=e.itemProps===!0?typeof l=="object"&&l!=null&&!Array.isArray(l)?"children"in l?Me(l,["children"]):l:void 0:O(l,e.itemProps),r={title:t,value:a,...n};return{title:String(r.title??""),value:r.value,props:r,children:Array.isArray(i)?Ke(e,i):void 0,raw:l}}function Ke(e,l){const t=je(e,["itemTitle","itemValue","itemChildren","itemProps","returnObject","valueComparator"]),a=[];for(const i of l)a.push(le(t,i));return a}function ea(e){const l=p(()=>Ke(e,e.items)),t=p(()=>l.value.some(u=>u.value===null)),a=_(new Map),i=_([]);St(()=>{const u=l.value,d=new Map,c=[];for(let y=0;y<u.length;y++){const f=u[y];if(xe(f.value)||f.value===null){let h=d.get(f.value);h||(h=[],d.set(f.value,h)),h.push(f)}else c.push(f)}a.value=d,i.value=c});function n(u){const d=a.value,c=l.value,y=i.value,f=t.value,h=e.returnObject,b=!!e.valueComparator,s=e.valueComparator||ht,o=je(e,["itemTitle","itemValue","itemChildren","itemProps","returnObject","valueComparator"]),v=[];e:for(const g of u){if(!f&&g===null)continue;if(h&&typeof g=="string"){v.push(le(o,g));continue}const C=d.get(g);if(b||!C){for(const A of b?c:y)if(s(g,A.value)){v.push(A);continue e}v.push(le(o,g));continue}v.push(...C)}return v}function r(u){return e.returnObject?u.map(d=>{let{raw:c}=d;return c}):u.map(d=>{let{value:c}=d;return c})}return{items:l,transformIn:n,transformOut:r}}function qt(e,l){const t=O(l,e.itemType,"item"),a=xe(l)?l:O(l,e.itemTitle),i=O(l,e.itemValue,void 0),n=O(l,e.itemChildren),r=e.itemProps===!0?Me(l,["children"]):O(l,e.itemProps),u={title:a,value:i,...r};return{type:t,title:u.title,value:u.value,props:u,children:t==="item"&&n?Xe(e,n):void 0,raw:l}}function Xe(e,l){const t=[];for(const a of l)t.push(qt(e,a));return t}function zt(e){return{items:p(()=>Xe(e,e.items))}}const Wt=j({baseColor:String,activeColor:String,activeClass:String,bgColor:String,disabled:Boolean,expandIcon:T,collapseIcon:T,lines:{type:[Boolean,String],default:"one"},slim:Boolean,nav:Boolean,"onClick:open":D(),"onClick:select":D(),"onUpdate:opened":D(),...jt({selectStrategy:"single-leaf",openStrategy:"list"}),..._e(),...$(),...Te(),...Be(),...Oe(),itemType:{type:String,default:"type"},...Xt(),...Le(),...U(),...Pe(),...Ve({variant:"text"})},"VList"),ta=N()({name:"VList",props:Wt(),emits:{"update:selected":e=>!0,"update:activated":e=>!0,"update:opened":e=>!0,"click:open":e=>!0,"click:activate":e=>!0,"click:select":e=>!0},setup(e,l){let{slots:t}=l;const{items:a}=zt(e),{themeClasses:i}=pe(e),{backgroundColorClasses:n,backgroundColorStyles:r}=pt(()=>e.bgColor),{borderClasses:u}=ke(e),{densityClasses:d}=Ce(e),{dimensionStyles:c}=Ie(e),{elevationClasses:y}=we(e),{roundedClasses:f}=Ae(e),{children:h,open:b,parents:s,select:o,getPath:v}=Mt(e),g=k(()=>e.lines?`v-list--${e.lines}-line`:void 0),C=k(()=>e.activeColor),A=k(()=>e.baseColor),E=k(()=>e.color);Fe(),kt({VListGroup:{activeColor:C,baseColor:A,color:E,expandIcon:k(()=>e.expandIcon),collapseIcon:k(()=>e.collapseIcon)},VListItem:{activeClass:k(()=>e.activeClass),activeColor:C,baseColor:A,color:E,density:k(()=>e.density),disabled:k(()=>e.disabled),lines:k(()=>e.lines),nav:k(()=>e.nav),slim:k(()=>e.slim),variant:k(()=>e.variant)}});const M=_(!1),V=L();function z(w){M.value=!0}function W(w){M.value=!1}function J(w){var x;!M.value&&!(w.relatedTarget&&((x=V.value)!=null&&x.contains(w.relatedTarget)))&&B()}function H(w){const x=w.target;if(!(!V.value||["INPUT","TEXTAREA"].includes(x.tagName))){if(w.key==="ArrowDown")B("next");else if(w.key==="ArrowUp")B("prev");else if(w.key==="Home")B("first");else if(w.key==="End")B("last");else return;w.preventDefault()}}function Q(w){M.value=!0}function B(w){if(V.value)return Ct(V.value,w)}return R(()=>m(e.tag,{ref:V,class:["v-list",{"v-list--disabled":e.disabled,"v-list--nav":e.nav,"v-list--slim":e.slim},i.value,n.value,u.value,d.value,y.value,g.value,f.value,e.class],style:[r.value,c.value,e.style],tabindex:e.disabled?-1:0,role:"listbox","aria-activedescendant":void 0,onFocusin:z,onFocusout:W,onFocus:J,onKeydown:H,onMousedown:Q},{default:()=>[m(He,{items:a.value,returnObject:e.returnObject},t)]})),{open:b,select:o,focus:B,children:h,parents:s,getPath:v}}});export{ta as V,ye as a,Rt as b,Ht as c,Gt as d,Xt as m,ea as u};
