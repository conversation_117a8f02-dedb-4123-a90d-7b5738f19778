import{aM as He,bV as De,a8 as Ie,a9 as z,b as V,p as $,T as me,bW as et,V as N,bX as tt,w as B,aX as be,bY as ke,bZ as ce,b_ as ue,b4 as H,a$ as Oe,r as W,b1 as j,b3 as Ee,b6 as nt,a_ as D,b$ as Ae,c0 as Pe,aY as xe,c1 as $e,c2 as ot,b2 as se,N as We,c3 as Ce,b8 as at,aQ as X,aP as rt,D as it,W as ye,c4 as qe,bn as Te,bp as st,bl as lt,L as ct,an as ut,aI as ft,af as dt,aa as vt,aw as mt,aO as yt,ag as gt,aC as ht,bm as wt,c5 as bt,ac as Et,c6 as xt,H as St,I as pt,bw as kt,F as Ot,c7 as At}from"./main-CWjS3hwz.js";import{a as J,g as je,n as ze,b as Pt,d as Ct,s as ge,B as ne,c as Fe}from"./forwardRefs-B931MWyl.js";import{m as Tt,M as Ft}from"./VImg-B7Lnz0wk.js";const oe=new WeakMap;function Lt(e,n){Object.keys(n).forEach(t=>{if(He(t)){const a=De(t),o=oe.get(e);if(n[t]==null)o==null||o.forEach(i=>{const[s,r]=i;s===a&&(e.removeEventListener(a,r),o.delete(i))});else if(!o||![...o].some(i=>i[0]===a&&i[1]===n[t])){e.addEventListener(a,n[t]);const i=o||new Set;i.add([a,n[t]]),oe.has(e)||oe.set(e,i)}}else n[t]==null?e.removeAttribute(t):e.setAttribute(t,n[t])})}function Bt(e,n){Object.keys(n).forEach(t=>{if(He(t)){const a=De(t),o=oe.get(e);o==null||o.forEach(i=>{const[s,r]=i;s===a&&(e.removeEventListener(a,r),o.delete(i))})}else e.removeAttribute(t)})}function Ye(e){if(typeof e.getRootNode!="function"){for(;e.parentNode;)e=e.parentNode;return e!==document?null:document}const n=e.getRootNode();return n!==document&&n.getRootNode({composed:!0})!==document?null:n}function Mt(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;for(;e;){if(n?Rt(e):Se(e))return e;e=e.parentElement}return document.scrollingElement}function re(e,n){const t=[];if(n&&e&&!n.contains(e))return t;for(;e&&(Se(e)&&t.push(e),e!==n);)e=e.parentElement;return t}function Se(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return!1;const n=window.getComputedStyle(e);return n.overflowY==="scroll"||n.overflowY==="auto"&&e.scrollHeight>e.clientHeight}function Rt(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return!1;const n=window.getComputedStyle(e);return["scroll","auto"].includes(n.overflowY)}function Nt(e){for(;e;){if(window.getComputedStyle(e).position==="fixed")return!0;e=e.offsetParent}return!1}const _t=z({target:[Object,Array]},"v-dialog-transition"),fe=new WeakMap,gn=Ie()({name:"VDialogTransition",props:_t(),setup(e,n){let{slots:t}=n;const a={onBeforeEnter(o){o.style.pointerEvents="none",o.style.visibility="hidden"},async onEnter(o,i){var b;await new Promise(h=>requestAnimationFrame(h)),await new Promise(h=>requestAnimationFrame(h)),o.style.visibility="";const s=Be(e.target,o),{x:r,y:u,sx:d,sy:l,speed:m}=s;fe.set(o,s);const p=J(o,[{transform:`translate(${r}px, ${u}px) scale(${d}, ${l})`,opacity:0},{}],{duration:225*m,easing:Ct});(b=Le(o))==null||b.forEach(h=>{J(h,[{opacity:0},{opacity:0,offset:.33},{}],{duration:225*2*m,easing:ge})}),p.finished.then(()=>i())},onAfterEnter(o){o.style.removeProperty("pointer-events")},onBeforeLeave(o){o.style.pointerEvents="none"},async onLeave(o,i){var b;await new Promise(h=>requestAnimationFrame(h));let s;!fe.has(o)||Array.isArray(e.target)||e.target.offsetParent||e.target.getClientRects().length?s=Be(e.target,o):s=fe.get(o);const{x:r,y:u,sx:d,sy:l,speed:m}=s;J(o,[{},{transform:`translate(${r}px, ${u}px) scale(${d}, ${l})`,opacity:0}],{duration:125*m,easing:Pt}).finished.then(()=>i()),(b=Le(o))==null||b.forEach(h=>{J(h,[{},{opacity:0,offset:.2},{opacity:0}],{duration:125*2*m,easing:ge})})},onAfterLeave(o){o.style.removeProperty("pointer-events")}};return()=>e.target?V(me,$({name:"dialog-transition"},a,{css:!1}),t):V(me,{name:"dialog-transition"},t)}});function Le(e){var t;const n=(t=e.querySelector(":scope > .v-card, :scope > .v-sheet, :scope > .v-list"))==null?void 0:t.children;return n&&[...n]}function Be(e,n){const t=je(e),a=ze(n),[o,i]=getComputedStyle(n).transformOrigin.split(" ").map(f=>parseFloat(f)),[s,r]=getComputedStyle(n).getPropertyValue("--v-overlay-anchor-origin").split(" ");let u=t.left+t.width/2;s==="left"||r==="left"?u-=t.width/2:(s==="right"||r==="right")&&(u+=t.width/2);let d=t.top+t.height/2;s==="top"||r==="top"?d-=t.height/2:(s==="bottom"||r==="bottom")&&(d+=t.height/2);const l=t.width/a.width,m=t.height/a.height,p=Math.max(1,l,m),b=l/p||0,h=m/p||0,T=a.width*a.height/(window.innerWidth*window.innerHeight),F=T>.12?Math.min(1.5,(T-.12)*10+1):1;return{x:u-(o+a.left),y:d-(i+a.top),sx:b,sy:h,speed:F}}function de(e,n){return{x:e.x+n.x,y:e.y+n.y}}function Vt(e,n){return{x:e.x-n.x,y:e.y-n.y}}function Me(e,n){if(e.side==="top"||e.side==="bottom"){const{side:t,align:a}=e,o=a==="left"?0:a==="center"?n.width/2:a==="right"?n.width:a,i=t==="top"?0:t==="bottom"?n.height:t;return de({x:o,y:i},n)}else if(e.side==="left"||e.side==="right"){const{side:t,align:a}=e,o=t==="left"?0:t==="right"?n.width:t,i=a==="top"?0:a==="center"?n.height/2:a==="bottom"?n.height:a;return de({x:o,y:i},n)}return de({x:n.width/2,y:n.height/2},n)}const Xe={static:It,connected:Wt},Ht=z({locationStrategy:{type:[String,Function],default:"static",validator:e=>typeof e=="function"||e in Xe},location:{type:String,default:"bottom"},origin:{type:String,default:"auto"},offset:[Number,String,Array]},"VOverlay-location-strategies");function Dt(e,n){const t=W({}),a=W();j&&Ee(()=>!!(n.isActive.value&&e.locationStrategy),i=>{var s,r;B(()=>e.locationStrategy,i),D(()=>{window.removeEventListener("resize",o),a.value=void 0}),window.addEventListener("resize",o,{passive:!0}),typeof e.locationStrategy=="function"?a.value=(s=e.locationStrategy(n,e,t))==null?void 0:s.updateLocation:a.value=(r=Xe[e.locationStrategy](n,e,t))==null?void 0:r.updateLocation});function o(i){var s;(s=a.value)==null||s.call(a,i)}return{contentStyles:t,updateLocation:a}}function It(){}function $t(e,n){const t=ze(e);return n?t.x+=parseFloat(e.style.right||0):t.x-=parseFloat(e.style.left||0),t.y-=parseFloat(e.style.top||0),t}function Wt(e,n,t){(Array.isArray(e.target.value)||Nt(e.target.value))&&Object.assign(t.value,{position:"fixed",top:0,[e.isRtl.value?"right":"left"]:0});const{preferredAnchor:o,preferredOrigin:i}=et(()=>{const f=Ae(n.location,e.isRtl.value),g=n.origin==="overlap"?f:n.origin==="auto"?ue(f):Ae(n.origin,e.isRtl.value);return f.side===g.side&&f.align===ce(g).align?{preferredAnchor:Pe(f),preferredOrigin:Pe(g)}:{preferredAnchor:f,preferredOrigin:g}}),[s,r,u,d]=["minWidth","minHeight","maxWidth","maxHeight"].map(f=>N(()=>{const g=parseFloat(n[f]);return isNaN(g)?1/0:g})),l=N(()=>{if(Array.isArray(n.offset))return n.offset;if(typeof n.offset=="string"){const f=n.offset.split(" ").map(parseFloat);return f.length<2&&f.push(0),f}return typeof n.offset=="number"?[n.offset,0]:[0,0]});let m=!1,p=-1;const b=new tt(4),h=new ResizeObserver(()=>{if(!m)return;if(requestAnimationFrame(g=>{g!==p&&b.clear(),requestAnimationFrame(k=>{p=k})}),b.isFull){const g=b.values();if(nt(g.at(-1),g.at(-3)))return}const f=F();f&&b.push(f.flipped)});B([e.target,e.contentEl],(f,g)=>{let[k,E]=f,[x,M]=g;x&&!Array.isArray(x)&&h.unobserve(x),k&&!Array.isArray(k)&&h.observe(k),M&&h.unobserve(M),E&&h.observe(E)},{immediate:!0}),D(()=>{h.disconnect()});let T=new ne({x:0,y:0,width:0,height:0});function F(){if(m=!1,requestAnimationFrame(()=>m=!0),!e.target.value||!e.contentEl.value)return;(Array.isArray(e.target.value)||e.target.value.offsetParent||e.target.value.getClientRects().length)&&(T=je(e.target.value));const f=$t(e.contentEl.value,e.isRtl.value),g=re(e.contentEl.value),k=12;g.length||(g.push(document.documentElement),e.contentEl.value.style.top&&e.contentEl.value.style.left||(f.x-=parseFloat(document.documentElement.style.getPropertyValue("--v-body-scroll-x")||0),f.y-=parseFloat(document.documentElement.style.getPropertyValue("--v-body-scroll-y")||0)));const E=g.reduce((A,S)=>{const y=S.getBoundingClientRect(),w=new ne({x:S===document.documentElement?0:y.x,y:S===document.documentElement?0:y.y,width:S.clientWidth,height:S.clientHeight});return A?new ne({x:Math.max(A.left,w.left),y:Math.max(A.top,w.top),width:Math.min(A.right,w.right)-Math.max(A.left,w.left),height:Math.min(A.bottom,w.bottom)-Math.max(A.top,w.top)}):w},void 0);E.x+=k,E.y+=k,E.width-=k*2,E.height-=k*2;let x={anchor:o.value,origin:i.value};function M(A){const S=new ne(f),y=Me(A.anchor,T),w=Me(A.origin,S);let{x:R,y:_}=Vt(y,w);switch(A.anchor.side){case"top":_-=l.value[0];break;case"bottom":_+=l.value[0];break;case"left":R-=l.value[0];break;case"right":R+=l.value[0];break}switch(A.anchor.align){case"top":_-=l.value[1];break;case"bottom":_+=l.value[1];break;case"left":R-=l.value[1];break;case"right":R+=l.value[1];break}return S.x+=R,S.y+=_,S.width=Math.min(S.width,u.value),S.height=Math.min(S.height,d.value),{overflows:Fe(S,E),x:R,y:_}}let P=0,c=0;const O={x:0,y:0},Y={x:!1,y:!1};let le=-1;for(;!(le++>10);){const{x:A,y:S,overflows:y}=M(x);P+=A,c+=S,f.x+=A,f.y+=S;{const w=ke(x.anchor),R=y.x.before||y.x.after,_=y.y.before||y.y.after;let U=!1;if(["x","y"].forEach(C=>{if(C==="x"&&R&&!Y.x||C==="y"&&_&&!Y.y){const I={anchor:{...x.anchor},origin:{...x.origin}},ee=C==="x"?w==="y"?ce:ue:w==="y"?ue:ce;I.anchor=ee(I.anchor),I.origin=ee(I.origin);const{overflows:q}=M(I);(q[C].before<=y[C].before&&q[C].after<=y[C].after||q[C].before+q[C].after<(y[C].before+y[C].after)/2)&&(x=I,U=Y[C]=!0)}}),U)continue}y.x.before&&(P+=y.x.before,f.x+=y.x.before),y.x.after&&(P-=y.x.after,f.x-=y.x.after),y.y.before&&(c+=y.y.before,f.y+=y.y.before),y.y.after&&(c-=y.y.after,f.y-=y.y.after);{const w=Fe(f,E);O.x=E.width-w.x.before-w.x.after,O.y=E.height-w.y.before-w.y.after,P+=w.x.before,f.x+=w.x.before,c+=w.y.before,f.y+=w.y.before}break}const K=ke(x.anchor);return Object.assign(t.value,{"--v-overlay-anchor-origin":`${x.anchor.side} ${x.anchor.align}`,transformOrigin:`${x.origin.side} ${x.origin.align}`,top:H(ve(c)),left:e.isRtl.value?void 0:H(ve(P)),right:e.isRtl.value?H(ve(-P)):void 0,minWidth:H(K==="y"?Math.min(s.value,T.width):s.value),maxWidth:H(Re(Oe(O.x,s.value===1/0?0:s.value,u.value))),maxHeight:H(Re(Oe(O.y,r.value===1/0?0:r.value,d.value)))}),{available:O,contentBox:f,flipped:Y}}return B(()=>[o.value,i.value,n.offset,n.minWidth,n.minHeight,n.maxWidth,n.maxHeight],()=>F()),be(()=>{const f=F();if(!f)return;const{available:g,contentBox:k}=f;k.height>g.y&&requestAnimationFrame(()=>{F(),requestAnimationFrame(()=>{F()})})}),{updateLocation:F}}function ve(e){return Math.round(e*devicePixelRatio)/devicePixelRatio}function Re(e){return Math.ceil(e*devicePixelRatio)/devicePixelRatio}let he=!0;const ie=[];function qt(e){!he||ie.length?(ie.push(e),we()):(he=!1,e(),we())}let Ne=-1;function we(){cancelAnimationFrame(Ne),Ne=requestAnimationFrame(()=>{const e=ie.shift();e&&e(),ie.length?we():he=!0})}const ae={none:null,close:Yt,block:Xt,reposition:Kt},jt=z({scrollStrategy:{type:[String,Function],default:"block",validator:e=>typeof e=="function"||e in ae}},"VOverlay-scroll-strategies");function zt(e,n){if(!j)return;let t;xe(async()=>{t==null||t.stop(),n.isActive.value&&e.scrollStrategy&&(t=$e(),await new Promise(a=>setTimeout(a)),t.active&&t.run(()=>{var a;typeof e.scrollStrategy=="function"?e.scrollStrategy(n,e,t):(a=ae[e.scrollStrategy])==null||a.call(ae,n,e,t)}))}),D(()=>{t==null||t.stop()})}function Yt(e){function n(t){e.isActive.value=!1}Ke(e.targetEl.value??e.contentEl.value,n)}function Xt(e,n){var s;const t=(s=e.root.value)==null?void 0:s.offsetParent,a=[...new Set([...re(e.targetEl.value,n.contained?t:void 0),...re(e.contentEl.value,n.contained?t:void 0)])].filter(r=>!r.classList.contains("v-overlay-scroll-blocked")),o=window.innerWidth-document.documentElement.offsetWidth,i=(r=>Se(r)&&r)(t||document.documentElement);i&&e.root.value.classList.add("v-overlay--scroll-blocked"),a.forEach((r,u)=>{r.style.setProperty("--v-body-scroll-x",H(-r.scrollLeft)),r.style.setProperty("--v-body-scroll-y",H(-r.scrollTop)),r!==document.documentElement&&r.style.setProperty("--v-scrollbar-offset",H(o)),r.classList.add("v-overlay-scroll-blocked")}),D(()=>{a.forEach((r,u)=>{const d=parseFloat(r.style.getPropertyValue("--v-body-scroll-x")),l=parseFloat(r.style.getPropertyValue("--v-body-scroll-y")),m=r.style.scrollBehavior;r.style.scrollBehavior="auto",r.style.removeProperty("--v-body-scroll-x"),r.style.removeProperty("--v-body-scroll-y"),r.style.removeProperty("--v-scrollbar-offset"),r.classList.remove("v-overlay-scroll-blocked"),r.scrollLeft=-d,r.scrollTop=-l,r.style.scrollBehavior=m}),i&&e.root.value.classList.remove("v-overlay--scroll-blocked")})}function Kt(e,n,t){let a=!1,o=-1,i=-1;function s(r){qt(()=>{var l,m;const u=performance.now();(m=(l=e.updateLocation).value)==null||m.call(l,r),a=(performance.now()-u)/(1e3/60)>2})}i=(typeof requestIdleCallback>"u"?r=>r():requestIdleCallback)(()=>{t.run(()=>{Ke(e.targetEl.value??e.contentEl.value,r=>{a?(cancelAnimationFrame(o),o=requestAnimationFrame(()=>{o=requestAnimationFrame(()=>{s(r)})})):s(r)})})}),D(()=>{typeof cancelIdleCallback<"u"&&cancelIdleCallback(i),cancelAnimationFrame(o)})}function Ke(e,n){const t=[document,...re(e)];t.forEach(a=>{a.addEventListener("scroll",n,{passive:!0})}),D(()=>{t.forEach(a=>{a.removeEventListener("scroll",n)})})}const Ut=Symbol.for("vuetify:v-menu"),Gt=z({closeDelay:[Number,String],openDelay:[Number,String]},"delay");function Zt(e,n){let t=()=>{};function a(s){t==null||t();const r=Number(s?e.openDelay:e.closeDelay);return new Promise(u=>{t=ot(r,()=>{n==null||n(s),u(s)})})}function o(){return a(!0)}function i(){return a(!1)}return{clearDelay:t,runOpenDelay:o,runCloseDelay:i}}const Qt=z({target:[String,Object],activator:[String,Object],activatorProps:{type:Object,default:()=>({})},openOnClick:{type:Boolean,default:void 0},openOnHover:Boolean,openOnFocus:{type:Boolean,default:void 0},closeOnContentClick:Boolean,...Gt()},"VOverlay-activator");function Jt(e,n){let{isActive:t,isTop:a,contentEl:o}=n;const i=se("useActivator"),s=W();let r=!1,u=!1,d=!0;const l=N(()=>e.openOnFocus||e.openOnFocus==null&&e.openOnHover),m=N(()=>e.openOnClick||e.openOnClick==null&&!e.openOnHover&&!l.value),{runOpenDelay:p,runCloseDelay:b}=Zt(e,c=>{c===(e.openOnHover&&r||l.value&&u)&&!(e.openOnHover&&t.value&&!a.value)&&(t.value!==c&&(d=!0),t.value=c)}),h=W(),T={onClick:c=>{c.stopPropagation(),s.value=c.currentTarget||c.target,t.value||(h.value=[c.clientX,c.clientY]),t.value=!t.value},onMouseenter:c=>{var O;(O=c.sourceCapabilities)!=null&&O.firesTouchEvents||(r=!0,s.value=c.currentTarget||c.target,p())},onMouseleave:c=>{r=!1,b()},onFocus:c=>{at(c.target,":focus-visible")!==!1&&(u=!0,c.stopPropagation(),s.value=c.currentTarget||c.target,p())},onBlur:c=>{u=!1,c.stopPropagation(),b()}},F=N(()=>{const c={};return m.value&&(c.onClick=T.onClick),e.openOnHover&&(c.onMouseenter=T.onMouseenter,c.onMouseleave=T.onMouseleave),l.value&&(c.onFocus=T.onFocus,c.onBlur=T.onBlur),c}),f=N(()=>{const c={};if(e.openOnHover&&(c.onMouseenter=()=>{r=!0,p()},c.onMouseleave=()=>{r=!1,b()}),l.value&&(c.onFocusin=()=>{u=!0,p()},c.onFocusout=()=>{u=!1,b()}),e.closeOnContentClick){const O=We(Ut,null);c.onClick=()=>{t.value=!1,O==null||O.closeParents()}}return c}),g=N(()=>{const c={};return e.openOnHover&&(c.onMouseenter=()=>{d&&(r=!0,d=!1,p())},c.onMouseleave=()=>{r=!1,b()}),c});B(a,c=>{var O;c&&(e.openOnHover&&!r&&(!l.value||!u)||l.value&&!u&&(!e.openOnHover||!r))&&!((O=o.value)!=null&&O.contains(document.activeElement))&&(t.value=!1)}),B(t,c=>{c||setTimeout(()=>{h.value=void 0})},{flush:"post"});const k=Ce();xe(()=>{k.value&&be(()=>{s.value=k.el})});const E=Ce(),x=N(()=>e.target==="cursor"&&h.value?h.value:E.value?E.el:Ue(e.target,i)||s.value),M=N(()=>Array.isArray(x.value)?void 0:x.value);let P;return B(()=>!!e.activator,c=>{c&&j?(P=$e(),P.run(()=>{en(e,i,{activatorEl:s,activatorEvents:F})})):P&&P.stop()},{flush:"post",immediate:!0}),D(()=>{P==null||P.stop()}),{activatorEl:s,activatorRef:k,target:x,targetEl:M,targetRef:E,activatorEvents:F,contentEvents:f,scrimEvents:g}}function en(e,n,t){let{activatorEl:a,activatorEvents:o}=t;B(()=>e.activator,(u,d)=>{if(d&&u!==d){const l=r(d);l&&s(l)}u&&be(()=>i())},{immediate:!0}),B(()=>e.activatorProps,()=>{i()}),D(()=>{s()});function i(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:r(),d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e.activatorProps;u&&Lt(u,$(o.value,d))}function s(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:r(),d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e.activatorProps;u&&Bt(u,$(o.value,d))}function r(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:e.activator;const d=Ue(u,n);return a.value=(d==null?void 0:d.nodeType)===Node.ELEMENT_NODE?d:void 0,a.value}}function Ue(e,n){var a,o;if(!e)return;let t;if(e==="parent"){let i=(o=(a=n==null?void 0:n.proxy)==null?void 0:a.$el)==null?void 0:o.parentNode;for(;i!=null&&i.hasAttribute("data-no-activator");)i=i.parentNode;t=i}else typeof e=="string"?t=document.querySelector(e):"$el"in e?t=e.$el:t=e;return t}function tn(){if(!j)return X(!1);const{ssr:e}=rt();if(e){const n=X(!1);return it(()=>{n.value=!0}),n}else return X(!0)}const nn=z({eager:Boolean},"lazy");function on(e,n){const t=X(!1),a=ye(()=>t.value||e.eager||n.value);B(n,()=>t.value=!0);function o(){e.eager||(t.value=!1)}return{isBooted:t,hasContent:a,onAfterLeave:o}}function an(){const n=se("useScopeId").vnode.scopeId;return{scopeId:n?{[n]:""}:void 0}}const _e=Symbol.for("vuetify:stack"),Q=qe([]);function rn(e,n,t){const a=se("useStack"),o=!t,i=We(_e,void 0),s=qe({activeChildren:new Set});ct(_e,s);const r=X(Number(Te(n)));Ee(e,()=>{var m;const l=(m=Q.at(-1))==null?void 0:m[1];r.value=l?l+10:Number(Te(n)),o&&Q.push([a.uid,r.value]),i==null||i.activeChildren.add(a.uid),D(()=>{if(o){const p=lt(Q).findIndex(b=>b[0]===a.uid);Q.splice(p,1)}i==null||i.activeChildren.delete(a.uid)})});const u=X(!0);o&&xe(()=>{var m;const l=((m=Q.at(-1))==null?void 0:m[0])===a.uid;setTimeout(()=>u.value=l)});const d=ye(()=>!s.activeChildren.size);return{globalTop:st(u),localTop:d,stackStyles:ye(()=>({zIndex:r.value}))}}function sn(e){return{teleportTarget:N(()=>{const t=e();if(t===!0||!j)return;const a=t===!1?document.body:typeof t=="string"?document.querySelector(t):t;if(a==null)return;let o=[...a.children].find(i=>i.matches(".v-overlay-container"));return o||(o=document.createElement("div"),o.className="v-overlay-container",a.appendChild(o)),o})}}function ln(){return!0}function Ge(e,n,t){if(!e||Ze(e,t)===!1)return!1;const a=Ye(n);if(typeof ShadowRoot<"u"&&a instanceof ShadowRoot&&a.host===e.target)return!1;const o=(typeof t.value=="object"&&t.value.include||(()=>[]))();return o.push(n),!o.some(i=>i==null?void 0:i.contains(e.target))}function Ze(e,n){return(typeof n.value=="object"&&n.value.closeConditional||ln)(e)}function cn(e,n,t){const a=typeof t.value=="function"?t.value:t.value.handler;e.shadowTarget=e.target,n._clickOutside.lastMousedownWasOutside&&Ge(e,n,t)&&setTimeout(()=>{Ze(e,t)&&a&&a(e)},0)}function Ve(e,n){const t=Ye(e);n(document),typeof ShadowRoot<"u"&&t instanceof ShadowRoot&&n(t)}const un={mounted(e,n){const t=o=>cn(o,e,n),a=o=>{e._clickOutside.lastMousedownWasOutside=Ge(o,e,n)};Ve(e,o=>{o.addEventListener("click",t,!0),o.addEventListener("mousedown",a,!0)}),e._clickOutside||(e._clickOutside={lastMousedownWasOutside:!1}),e._clickOutside[n.instance.$.uid]={onClick:t,onMousedown:a}},beforeUnmount(e,n){e._clickOutside&&(Ve(e,t=>{var i;if(!t||!((i=e._clickOutside)!=null&&i[n.instance.$.uid]))return;const{onClick:a,onMousedown:o}=e._clickOutside[n.instance.$.uid];t.removeEventListener("click",a,!0),t.removeEventListener("mousedown",o,!0)}),delete e._clickOutside[n.instance.$.uid])}};function fn(e){const{modelValue:n,color:t,...a}=e;return V(me,{name:"fade-transition",appear:!0},{default:()=>[e.modelValue&&V("div",$({class:["v-overlay__scrim",e.color.backgroundColorClasses.value],style:e.color.backgroundColorStyles.value},a),null)]})}const dn=z({absolute:Boolean,attach:[Boolean,String,Object],closeOnBack:{type:Boolean,default:!0},contained:Boolean,contentClass:null,contentProps:null,disabled:Boolean,opacity:[Number,String],noClickAnimation:Boolean,modelValue:Boolean,persistent:Boolean,scrim:{type:[Boolean,String],default:!0},zIndex:{type:[Number,String],default:2e3},...Qt(),...dt(),...ft(),...nn(),...Ht(),...jt(),...ut(),...Tt()},"VOverlay"),hn=Ie()({name:"VOverlay",directives:{ClickOutside:un},inheritAttrs:!1,props:{_disableGlobalStack:Boolean,...dn()},emits:{"click:outside":e=>!0,"update:modelValue":e=>!0,keydown:e=>!0,afterEnter:()=>!0,afterLeave:()=>!0},setup(e,n){let{slots:t,attrs:a,emit:o}=n;const i=se("VOverlay"),s=W(),r=W(),u=W(),d=vt(e,"modelValue"),l=N({get:()=>d.value,set:v=>{v&&e.disabled||(d.value=v)}}),{themeClasses:m}=mt(e),{rtlClasses:p,isRtl:b}=yt(),{hasContent:h,onAfterLeave:T}=on(e,l),F=gt(()=>typeof e.scrim=="string"?e.scrim:null),{globalTop:f,localTop:g,stackStyles:k}=rn(l,()=>e.zIndex,e._disableGlobalStack),{activatorEl:E,activatorRef:x,target:M,targetEl:P,targetRef:c,activatorEvents:O,contentEvents:Y,scrimEvents:le}=Jt(e,{isActive:l,isTop:g,contentEl:u}),{teleportTarget:K}=sn(()=>{var G,Z,pe;const v=e.attach||e.contained;if(v)return v;const L=((G=E==null?void 0:E.value)==null?void 0:G.getRootNode())||((pe=(Z=i.proxy)==null?void 0:Z.$el)==null?void 0:pe.getRootNode());return L instanceof ShadowRoot?L:!1}),{dimensionStyles:A}=ht(e),S=tn(),{scopeId:y}=an();B(()=>e.disabled,v=>{v&&(l.value=!1)});const{contentStyles:w,updateLocation:R}=Dt(e,{isRtl:b,contentEl:u,target:M,isActive:l});zt(e,{root:s,contentEl:u,targetEl:P,isActive:l,updateLocation:R});function _(v){o("click:outside",v),e.persistent?te():l.value=!1}function U(v){return l.value&&f.value&&(!e.scrim||v.target===r.value||v instanceof MouseEvent&&v.shadowTarget===r.value)}j&&B(l,v=>{v?window.addEventListener("keydown",C):window.removeEventListener("keydown",C)},{immediate:!0}),wt(()=>{j&&window.removeEventListener("keydown",C)});function C(v){var L,G,Z;v.key==="Escape"&&f.value&&((L=u.value)!=null&&L.contains(document.activeElement)||o("keydown",v),e.persistent?te():(l.value=!1,(G=u.value)!=null&&G.contains(document.activeElement)&&((Z=E.value)==null||Z.focus())))}function I(v){v.key==="Escape"&&!f.value||o("keydown",v)}const ee=bt();Ee(()=>e.closeOnBack,()=>{At(ee,v=>{f.value&&l.value?(v(!1),e.persistent?te():l.value=!1):v()})});const q=W();B(()=>l.value&&(e.absolute||e.contained)&&K.value==null,v=>{if(v){const L=Mt(s.value);L&&L!==document.scrollingElement&&(q.value=L.scrollTop)}});function te(){e.noClickAnimation||u.value&&J(u.value,[{transformOrigin:"center"},{transform:"scale(1.03)"},{transformOrigin:"center"}],{duration:150,easing:ge})}function Qe(){o("afterEnter")}function Je(){T(),o("afterLeave")}return Et(()=>{var v;return V(Ot,null,[(v=t.activator)==null?void 0:v.call(t,{isActive:l.value,targetRef:c,props:$({ref:x},O.value,e.activatorProps)}),S.value&&h.value&&V(xt,{disabled:!K.value,to:K.value},{default:()=>[V("div",$({class:["v-overlay",{"v-overlay--absolute":e.absolute||e.contained,"v-overlay--active":l.value,"v-overlay--contained":e.contained},m.value,p.value,e.class],style:[k.value,{"--v-overlay-opacity":e.opacity,top:H(q.value)},e.style],ref:s,onKeydown:I},y,a),[V(fn,$({color:F,modelValue:l.value&&!!e.scrim,ref:r},le.value),null),V(Ft,{appear:!0,persisted:!0,transition:e.transition,target:M.value,onAfterEnter:Qe,onAfterLeave:Je},{default:()=>{var L;return[St(V("div",$({ref:u,class:["v-overlay__content",e.contentClass],style:[A.value,w.value]},Y.value,e.contentProps),[(L=t.default)==null?void 0:L.call(t,{isActive:l})]),[[pt,l.value],[kt("click-outside"),{handler:_,closeConditional:U,include:()=>[E.value]}]])]}})])]})])}),{activatorEl:E,scrimEl:r,target:M,animateClick:te,contentEl:u,globalTop:f,localTop:g,updateLocation:R}}});export{hn as V,gn as a,Ut as b,Mt as g,dn as m,an as u};
