import{a9 as I,an as Z,aJ as p,af as w,b6 as ee,aq as D,a8 as P,aa as S,ab as U,W as o,aR as le,ac as F,b as f,L as ae,a_ as te,bs as ne,aQ as N,r as q,bN as E,p as x,H as oe,bw as ue,F as re,X as ie,N as se,aB as ce,V as g,b5 as R,aj as de,ag as ve,aX as fe,b8 as me,ad as H}from"./main-CWjS3hwz.js";import{V as be,b as Ve,c as j,d as ye,e as Ce,f as ke}from"./VTextField-XbG62wJL.js";import{f as he}from"./forwardRefs-B931MWyl.js";const M=Symbol.for("vuetify:selection-control-group"),X=I({color:String,disabled:{type:Boolean,default:null},defaultsTarget:String,error:Boolean,id:String,inline:Boolean,falseIcon:D,trueIcon:D,ripple:{type:[Boolean,Object],default:!0},multiple:{type:Boolean,default:null},name:String,readonly:{type:Boolean,default:null},modelValue:null,type:String,valueComparator:{type:Function,default:ee},...w(),...p(),...Z()},"SelectionControlGroup"),Ie=I({...X({defaultsTarget:"VSelectionControl"})},"VSelectionControlGroup");P()({name:"VSelectionControlGroup",props:Ie(),emits:{"update:modelValue":e=>!0},setup(e,u){let{slots:r}=u;const l=S(e,"modelValue"),a=U(),s=o(()=>e.id||`v-selection-control-group-${a}`),c=o(()=>e.name||s.value),n=new Set;return ae(M,{modelValue:l,forceUpdate:()=>{n.forEach(t=>t())},onForceUpdate:t=>{n.add(t),te(()=>{n.delete(t)})}}),le({[e.defaultsTarget]:{color:o(()=>e.color),disabled:o(()=>e.disabled),density:o(()=>e.density),error:o(()=>e.error),inline:o(()=>e.inline),modelValue:l,multiple:o(()=>!!e.multiple||e.multiple==null&&Array.isArray(l.value)),name:c,falseIcon:o(()=>e.falseIcon),trueIcon:o(()=>e.trueIcon),readonly:o(()=>e.readonly),ripple:o(()=>e.ripple),type:o(()=>e.type),valueComparator:o(()=>e.valueComparator)}}),F(()=>{var t;return f("div",{class:["v-selection-control-group",{"v-selection-control-group--inline":e.inline},e.class],style:e.style,role:e.type==="radio"?"radiogroup":void 0},[(t=r.default)==null?void 0:t.call(r)])}),{}}});const J=I({label:String,baseColor:String,trueValue:null,falseValue:null,value:null,...w(),...X()},"VSelectionControl");function ge(e){const u=se(M,void 0),{densityClasses:r}=ce(e),l=S(e,"modelValue"),a=g(()=>e.trueValue!==void 0?e.trueValue:e.value!==void 0?e.value:!0),s=g(()=>e.falseValue!==void 0?e.falseValue:!1),c=g(()=>!!e.multiple||e.multiple==null&&Array.isArray(l.value)),n=g({get(){const v=u?u.modelValue.value:l.value;return c.value?R(v).some(m=>e.valueComparator(m,a.value)):e.valueComparator(v,a.value)},set(v){if(e.readonly)return;const m=v?a.value:s.value;let V=m;c.value&&(V=v?[...R(l.value),m]:R(l.value).filter(b=>!e.valueComparator(b,a.value))),u?u.modelValue.value=V:l.value=V}}),{textColorClasses:t,textColorStyles:i}=de(()=>{if(!(e.error||e.disabled))return n.value?e.color:e.baseColor}),{backgroundColorClasses:d,backgroundColorStyles:y}=ve(()=>n.value&&!e.error&&!e.disabled?e.color:e.baseColor),k=g(()=>n.value?e.trueIcon:e.falseIcon);return{group:u,densityClasses:r,trueValue:a,falseValue:s,model:n,textColorClasses:t,textColorStyles:i,backgroundColorClasses:d,backgroundColorStyles:y,icon:k}}const L=P()({name:"VSelectionControl",directives:{Ripple:ne},inheritAttrs:!1,props:J(),emits:{"update:modelValue":e=>!0},setup(e,u){let{attrs:r,slots:l}=u;const{group:a,densityClasses:s,icon:c,model:n,textColorClasses:t,textColorStyles:i,backgroundColorClasses:d,backgroundColorStyles:y,trueValue:k}=ge(e),v=U(),m=N(!1),V=N(!1),b=q(),h=o(()=>e.id||`input-${v}`),B=o(()=>!e.disabled&&!e.readonly);a==null||a.onForceUpdate(()=>{b.value&&(b.value.checked=n.value)});function A(C){B.value&&(m.value=!0,me(C.target,":focus-visible")!==!1&&(V.value=!0))}function G(){m.value=!1,V.value=!1}function W(C){C.stopPropagation()}function z(C){if(!B.value){b.value&&(b.value.checked=n.value);return}e.readonly&&a&&fe(()=>a.forceUpdate()),n.value=C.target.checked}return F(()=>{var $,_;const C=l.label?l.label({label:e.label,props:{for:h.value}}):e.label,[K,Y]=E(r),T=f("input",x({ref:b,checked:n.value,disabled:!!e.disabled,id:h.value,onBlur:G,onFocus:A,onInput:z,"aria-disabled":!!e.disabled,"aria-label":e.label,type:e.type,value:k.value,name:e.name,"aria-checked":e.type==="checkbox"?n.value:void 0},Y),null);return f("div",x({class:["v-selection-control",{"v-selection-control--dirty":n.value,"v-selection-control--disabled":e.disabled,"v-selection-control--error":e.error,"v-selection-control--focused":m.value,"v-selection-control--focus-visible":V.value,"v-selection-control--inline":e.inline},s.value,e.class]},K,{style:e.style}),[f("div",{class:["v-selection-control__wrapper",t.value],style:i.value},[($=l.default)==null?void 0:$.call(l,{backgroundColorClasses:d,backgroundColorStyles:y}),oe(f("div",{class:["v-selection-control__input"]},[((_=l.input)==null?void 0:_.call(l,{model:n,textColorClasses:t,textColorStyles:i,backgroundColorClasses:d,backgroundColorStyles:y,inputNode:T,icon:c.value,props:{onFocus:A,onBlur:G,id:h.value}}))??f(re,null,[c.value&&f(ie,{key:"icon",icon:c.value},null),T])]),[[ue("ripple"),e.ripple&&[!e.disabled&&!e.readonly,null,["center","circle"]]]])]),C&&f(be,{for:h.value,onClick:W},{default:()=>[C]})])}),{isFocused:m,input:b}}}),Q=I({indeterminate:Boolean,indeterminateIcon:{type:D,default:"$checkboxIndeterminate"},...J({falseIcon:"$checkboxOff",trueIcon:"$checkboxOn"})},"VCheckboxBtn"),O=P()({name:"VCheckboxBtn",props:Q(),emits:{"update:modelValue":e=>!0,"update:indeterminate":e=>!0},setup(e,u){let{slots:r}=u;const l=S(e,"indeterminate"),a=S(e,"modelValue");function s(t){l.value&&(l.value=!1)}const c=o(()=>l.value?e.indeterminateIcon:e.falseIcon),n=o(()=>l.value?e.indeterminateIcon:e.trueIcon);return F(()=>{const t=H(L.filterProps(e),["modelValue"]);return f(L,x(t,{modelValue:a.value,"onUpdate:modelValue":[i=>a.value=i,s],class:["v-checkbox-btn",e.class],style:e.style,type:"checkbox",falseIcon:c.value,trueIcon:n.value,"aria-checked":l.value?"mixed":void 0}),r)}),{}}}),Se=I({...ye(),...H(Q(),["inline"])},"VCheckbox"),Ae=P()({name:"VCheckbox",inheritAttrs:!1,props:Se(),emits:{"update:modelValue":e=>!0,"update:focused":e=>!0},setup(e,u){let{attrs:r,slots:l}=u;const a=S(e,"modelValue"),{isFocused:s,focus:c,blur:n}=Ve(e),t=U();return F(()=>{const[i,d]=E(r),y=j.filterProps(e),k=O.filterProps(e);return f(j,x({class:["v-checkbox",e.class]},i,y,{modelValue:a.value,"onUpdate:modelValue":v=>a.value=v,id:e.id||`checkbox-${t}`,focused:s.value,style:e.style}),{...l,default:v=>{let{id:m,messagesId:V,isDisabled:b,isReadonly:h,isValid:B}=v;return f(O,x(k,{id:m.value,"aria-describedby":V.value,disabled:b.value,readonly:h.value},d,{error:B.value===!1,modelValue:a.value,"onUpdate:modelValue":A=>a.value=A,onFocus:c,onBlur:n}),l)}})}),{}}}),xe=I({...w(),...ke()},"VForm"),Re=P()({name:"VForm",props:xe(),emits:{"update:modelValue":e=>!0,submit:e=>!0},setup(e,u){let{slots:r,emit:l}=u;const a=Ce(e),s=q();function c(t){t.preventDefault(),a.reset()}function n(t){const i=t,d=a.validate();i.then=d.then.bind(d),i.catch=d.catch.bind(d),i.finally=d.finally.bind(d),l("submit",i),i.defaultPrevented||d.then(y=>{var v;let{valid:k}=y;k&&((v=s.value)==null||v.submit())}),i.preventDefault()}return F(()=>{var t;return f("form",{ref:s,class:["v-form",e.class],style:e.style,novalidate:!0,onReset:c,onSubmit:n},[(t=r.default)==null?void 0:t.call(r,a)])}),he(a,s)}});export{Re as V,Ae as a,O as b};
