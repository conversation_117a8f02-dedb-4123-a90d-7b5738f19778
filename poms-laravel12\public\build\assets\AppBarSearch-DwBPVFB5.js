import{a8 as P,a9 as K,ag as N,aC as U,aD as E,aw as j,ai as z,V as H,b5 as M,ac as X,b as s,p as O,an as Q,aH as q,aI as G,d as J,bS as W,r as w,w as C,g as Y,o as h,f as n,bT as B,a5 as Z,m as d,e as c,X as x,H as S,c as _,l as $,y as f,I as D,F as L,i as A,s as ee,t as R}from"./main-CWjS3hwz.js";import{C as ae}from"./vue3-perfect-scrollbar-chdc-gFa.js";import{V as te}from"./VCard-y1UgCbHQ.js";import{V as T}from"./VCardText-DnBTLtJ0.js";import{a as se}from"./VTextField-XbG62wJL.js";import{V as ie}from"./VDivider-DaCbr10A.js";import{V as oe,a as le}from"./VList-CUNWEV6C.js";import{V as re}from"./VDialog-GhSSCIsG.js";import{_ as ne}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./VAvatar-Ds2UYUGl.js";import"./VImg-B7Lnz0wk.js";import"./forwardRefs-B931MWyl.js";import"./VOverlay-zhJGflkj.js";const de={actions:"button@2",article:"heading, paragraph",avatar:"avatar",button:"button",card:"image, heading","card-avatar":"image, list-item-avatar",chip:"chip","date-picker":"list-item, heading, divider, date-picker-options, date-picker-days, actions","date-picker-options":"text, avatar@2","date-picker-days":"avatar@28",divider:"divider",heading:"heading",image:"image","list-item":"text","list-item-avatar":"avatar, text","list-item-two-line":"sentences","list-item-avatar-two-line":"avatar, sentences","list-item-three-line":"paragraph","list-item-avatar-three-line":"avatar, paragraph",ossein:"ossein",paragraph:"text@3",sentences:"text@2",subtitle:"text",table:"table-heading, table-thead, table-tbody, table-tfoot","table-heading":"chip, text","table-thead":"heading@6","table-tbody":"table-row-divider@6","table-row-divider":"table-row, divider","table-row":"text@6","table-tfoot":"text@2, avatar@2",text:"text"};function ce(e){let i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];return s("div",{class:["v-skeleton-loader__bone",`v-skeleton-loader__${e}`]},[i])}function I(e){const[i,t]=e.split("@");return Array.from({length:t}).map(()=>v(i))}function v(e){let i=[];if(!e)return i;const t=de[e];if(e!==t){if(e.includes(","))return F(e);if(e.includes("@"))return I(e);t.includes(",")?i=F(t):t.includes("@")?i=I(t):t&&i.push(v(t))}return[ce(e,i)]}function F(e){return e.replace(/\s/g,"").split(",").map(v)}const ue=K({boilerplate:Boolean,color:String,loading:Boolean,loadingText:{type:String,default:"$vuetify.loading"},type:{type:[String,Array],default:"ossein"},...G(),...q(),...Q()},"VSkeletonLoader"),pe=P()({name:"VSkeletonLoader",props:ue(),setup(e,i){let{slots:t}=i;const{backgroundColorClasses:p,backgroundColorStyles:b}=N(()=>e.color),{dimensionStyles:y}=U(e),{elevationClasses:m}=E(e),{themeClasses:k}=j(e),{t:o}=z(),u=H(()=>v(M(e.type).join(",")));return X(()=>{var a;const g=!t.default||e.loading,V=e.boilerplate||!g?{}:{ariaLive:"polite",ariaLabel:o(e.loadingText),role:"alert"};return s("div",O({class:["v-skeleton-loader",{"v-skeleton-loader--boilerplate":e.boilerplate},k.value,p.value,m.value],style:[b.value,g?y.value:{}]},V),[g?u.value:(a=t.default)==null?void 0:a.call(t)])}),{}}}),me={class:"d-flex align-center text-high-emphasis me-1"},ge={class:"d-flex align-start"},he={class:"h-100"},fe={class:"h-100"},ve={class:"app-bar-search-suggestions d-flex flex-column align-center justify-center text-high-emphasis pa-12"},be={class:"d-flex align-center flex-wrap justify-center gap-2 text-h5 mt-3"},ye=J({__name:"AppBarSearch",props:{isDialogVisible:{type:Boolean},searchResults:{},isLoading:{type:Boolean}},emits:["update:isDialogVisible","search"],setup(e,{emit:i}){const t=e,p=i,{ctrl_k:b,meta_k:y}=W({passive:!1,onEventFired(a){a.ctrlKey&&a.key==="k"&&a.type==="keydown"&&a.preventDefault()}}),m=w(),k=w(),o=w("");C([b,y],()=>{p("update:isDialogVisible",!0)});const u=()=>{o.value="",p("update:isDialogVisible",!1)},g=a=>{var r,l;a.key==="ArrowDown"?(a.preventDefault(),(r=m.value)==null||r.focus("next")):a.key==="ArrowUp"&&(a.preventDefault(),(l=m.value)==null||l.focus("prev"))},V=a=>{o.value="",p("update:isDialogVisible",a)};return C(()=>t.isDialogVisible,()=>{o.value=""}),(a,r)=>(h(),Y(re,{"max-width":"600","model-value":t.isDialogVisible,height:a.$vuetify.display.smAndUp?"531":"100%",fullscreen:a.$vuetify.display.width<600,class:"app-bar-search-dialog","onUpdate:modelValue":V,onKeyup:B(u,["esc"])},{default:n(()=>[s(te,{height:"100%",width:"100%",class:"position-relative"},{default:n(()=>[s(T,{class:"px-4",style:{"padding-block":"1rem 1.2rem"}},{default:n(()=>[s(se,{ref_key:"refSearchInput",ref:k,modelValue:d(o),"onUpdate:modelValue":[r[0]||(r[0]=l=>Z(o)?o.value=l:null),r[1]||(r[1]=l=>a.$emit("search",d(o)))],autofocus:"",density:"compact",variant:"plain",class:"app-bar-search-input",onKeyup:B(u,["esc"]),onKeydown:g},{"prepend-inner":n(()=>[c("div",me,[s(x,{size:"24",icon:"tabler-search"})])]),"append-inner":n(()=>[c("div",ge,[c("div",{class:"text-base text-disabled cursor-pointer me-3",onClick:u}," [esc] "),s(x,{icon:"tabler-x",size:"24",onClick:u})])]),_:1},8,["modelValue"])]),_:1}),s(ie),s(d(ae),{options:{wheelPropagation:!1,suppressScrollX:!0},class:"h-100"},{default:n(()=>[S(c("div",he,[f(a.$slots,"suggestions",{},void 0,!0)],512),[[D,!!t.searchResults&&!d(o)&&a.$slots.suggestions]]),a.isLoading?$("",!0):(h(),_(L,{key:0},[S(s(d(oe),{ref_key:"refSearchList",ref:m,density:"compact",class:"app-bar-search-list py-0"},{default:n(()=>[(h(!0),_(L,null,A(t.searchResults,l=>f(a.$slots,"searchResult",{key:l,item:l},()=>[s(d(le),null,{default:n(()=>[ee(R(l),1)]),_:2},1024)],!0)),128))]),_:3},512),[[D,d(o).length&&!!t.searchResults.length]]),S(c("div",fe,[f(a.$slots,"noData",{},()=>[s(T,{class:"h-100"},{default:n(()=>[c("div",ve,[s(x,{size:"64",icon:"tabler-file-alert"}),c("div",be,[r[2]||(r[2]=c("span",null,"No Result For ",-1)),c("span",null,'"'+R(d(o))+'"',1)]),f(a.$slots,"noDataSuggestion",{},void 0,!0)])]),_:3})],!0)],512),[[D,!t.searchResults.length&&d(o).length]])],64)),a.isLoading?(h(),_(L,{key:1},A(3,l=>s(pe,{key:l,type:"list-item-two-line"})),64)):$("",!0)]),_:3})]),_:3})]),_:3},8,["model-value","height","fullscreen"]))}}),Te=ne(ye,[["__scopeId","data-v-ef7d10b6"]]);export{Te as default};
