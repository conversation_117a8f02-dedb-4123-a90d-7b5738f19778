import{V as l,b as n}from"./VCard-y1UgCbHQ.js";import{V as s}from"./VCardText-DnBTLtJ0.js";import{d as i,c as d,b as t,f as a,o as m,s as p,e as o}from"./main-CWjS3hwz.js";import{b as r}from"./route-block-B_A1xBdJ.js";import"./VAvatar-Ds2UYUGl.js";import"./VImg-B7Lnz0wk.js";const u=i({__name:"tech-dashboard",setup(f){return(c,e)=>(m(),d("div",null,[t(l,null,{default:a(()=>[t(n,null,{default:a(()=>e[0]||(e[0]=[p("Technical Dashboard")])),_:1,__:[0]}),t(s,null,{default:a(()=>e[1]||(e[1]=[o("p",null,"Technical Dashboard functionality will be implemented here.",-1),o("p",null,"This page will show technical monitoring, system performance, and infrastructure status.",-1)])),_:1,__:[1]})]),_:1})]))}});typeof r=="function"&&r(u);export{u as default};
