import{a8 as D,a9 as C,aC as X,ac as E,b as a,aI as K,af as H,V as T,bQ as W,O as Y,T as Z,p as w,J as p,bR as M,ag as ee,ah as te,b2 as ne,aQ as h,r as re,w as P,bo as ae,aX as se,bm as ie,W as le,H as F,bw as oe,b4 as ue,ao as ce,F as de,I as ve}from"./main-CWjS3hwz.js";function ge(e){return{aspectStyles:T(()=>{const l=Number(e.aspectRatio);return l?{paddingBottom:String(1/l*100)+"%"}:void 0})}}const q=C({aspectRatio:[String,Number],contentClass:null,inline:Boolean,...H(),...K()},"VResponsive"),$=D()({name:"VResponsive",props:q(),setup(e,l){let{slots:s}=l;const{aspectStyles:n}=ge(e),{dimensionStyles:c}=X(e);return E(()=>{var d;return a("div",{class:["v-responsive",{"v-responsive--inline":e.inline},e.class],style:[c.value,e.style]},[a("div",{class:"v-responsive__sizer",style:n.value},null),(d=s.additional)==null?void 0:d.call(s),s.default&&a("div",{class:["v-responsive__content",e.contentClass]},[s.default()])])}),{}}}),me=C({transition:{type:null,default:"fade-transition",validator:e=>e!==!0}},"transition"),I=(e,l)=>{let{slots:s}=l;const{transition:n,disabled:c,group:d,...f}=e,{component:v=d?Y:Z,...b}=W(n)?n:{};let r;return W(n)?r=w(b,JSON.parse(JSON.stringify({disabled:c,group:d})),f):r=w({name:c||!n?"":n},f),p(v,r,s)};function fe(e,l){if(!M)return;const s=l.modifiers||{},n=l.value,{handler:c,options:d}=typeof n=="object"?n:{handler:n,options:{}},f=new IntersectionObserver(function(){var g;let v=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],b=arguments.length>1?arguments[1]:void 0;const r=(g=e._observe)==null?void 0:g[l.instance.$.uid];if(!r)return;const i=v.some(S=>S.isIntersecting);c&&(!s.quiet||r.init)&&(!s.once||i||r.init)&&c(i,v,b),i&&s.once?x(e,l):r.init=!0},d);e._observe=Object(e._observe),e._observe[l.instance.$.uid]={init:!1,observer:f},f.observe(e)}function x(e,l){var n;const s=(n=e._observe)==null?void 0:n[l.instance.$.uid];s&&(s.observer.unobserve(e),delete e._observe[l.instance.$.uid])}const be={mounted:fe,unmounted:x},Se=C({absolute:Boolean,alt:String,cover:Boolean,color:String,draggable:{type:[Boolean,String],default:void 0},eager:Boolean,gradient:String,lazySrc:String,options:{type:Object,default:()=>({root:void 0,rootMargin:void 0,threshold:void 0})},sizes:String,src:{type:[String,Object],default:""},crossorigin:String,referrerpolicy:String,srcset:String,position:String,...q(),...H(),...ce(),...me()},"VImg"),ye=D()({name:"VImg",directives:{intersect:be},props:Se(),emits:{loadstart:e=>!0,load:e=>!0,error:e=>!0},setup(e,l){let{emit:s,slots:n}=l;const{backgroundColorClasses:c,backgroundColorStyles:d}=ee(()=>e.color),{roundedClasses:f}=te(e),v=ne("VImg"),b=h(""),r=re(),i=h(e.eager?"loading":"idle"),g=h(),S=h(),u=T(()=>e.src&&typeof e.src=="object"?{src:e.src.src,srcset:e.srcset||e.src.srcset,lazySrc:e.lazySrc||e.src.lazySrc,aspect:Number(e.aspectRatio||e.src.aspect||0)}:{src:e.src,srcset:e.srcset,lazySrc:e.lazySrc,aspect:Number(e.aspectRatio||0)}),_=T(()=>u.value.aspect||g.value/S.value||0);P(()=>e.src,()=>{R(i.value!=="idle")}),P(_,(t,o)=>{!t&&o&&r.value&&y(r.value)}),ae(()=>R());function R(t){if(!(e.eager&&t)&&!(M&&!t&&!e.eager)){if(i.value="loading",u.value.lazySrc){const o=new Image;o.src=u.value.lazySrc,y(o,null)}u.value.src&&se(()=>{var o;s("loadstart",((o=r.value)==null?void 0:o.currentSrc)||u.value.src),setTimeout(()=>{var m;if(!v.isUnmounted)if((m=r.value)!=null&&m.complete){if(r.value.naturalWidth||k(),i.value==="error")return;_.value||y(r.value,null),i.value==="loading"&&V()}else _.value||y(r.value),B()})})}}function V(){var t;v.isUnmounted||(B(),y(r.value),i.value="loaded",s("load",((t=r.value)==null?void 0:t.currentSrc)||u.value.src))}function k(){var t;v.isUnmounted||(i.value="error",s("error",((t=r.value)==null?void 0:t.currentSrc)||u.value.src))}function B(){const t=r.value;t&&(b.value=t.currentSrc||t.src)}let z=-1;ie(()=>{clearTimeout(z)});function y(t){let o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:100;const m=()=>{if(clearTimeout(z),v.isUnmounted)return;const{naturalHeight:j,naturalWidth:U}=t;j||U?(g.value=U,S.value=j):!t.complete&&i.value==="loading"&&o!=null?z=window.setTimeout(m,o):(t.currentSrc.endsWith(".svg")||t.currentSrc.startsWith("data:image/svg+xml"))&&(g.value=1,S.value=1)};m()}const O=le(()=>({"v-img__img--cover":e.cover,"v-img__img--contain":!e.cover})),A=()=>{var m;if(!u.value.src||i.value==="idle")return null;const t=a("img",{class:["v-img__img",O.value],style:{objectPosition:e.position},crossorigin:e.crossorigin,src:u.value.src,srcset:u.value.srcset,alt:e.alt,referrerpolicy:e.referrerpolicy,draggable:e.draggable,sizes:e.sizes,ref:r,onLoad:V,onError:k},null),o=(m=n.sources)==null?void 0:m.call(n);return a(I,{transition:e.transition,appear:!0},{default:()=>[F(o?a("picture",{class:"v-img__picture"},[o,t]):t,[[ve,i.value==="loaded"]])]})},J=()=>a(I,{transition:e.transition},{default:()=>[u.value.lazySrc&&i.value!=="loaded"&&a("img",{class:["v-img__img","v-img__img--preload",O.value],style:{objectPosition:e.position},crossorigin:e.crossorigin,src:u.value.lazySrc,alt:e.alt,referrerpolicy:e.referrerpolicy,draggable:e.draggable},null)]}),L=()=>n.placeholder?a(I,{transition:e.transition,appear:!0},{default:()=>[(i.value==="loading"||i.value==="error"&&!n.error)&&a("div",{class:"v-img__placeholder"},[n.placeholder()])]}):null,Q=()=>n.error?a(I,{transition:e.transition,appear:!0},{default:()=>[i.value==="error"&&a("div",{class:"v-img__error"},[n.error()])]}):null,G=()=>e.gradient?a("div",{class:"v-img__gradient",style:{backgroundImage:`linear-gradient(${e.gradient})`}},null):null,N=h(!1);{const t=P(_,o=>{o&&(requestAnimationFrame(()=>{requestAnimationFrame(()=>{N.value=!0})}),t())})}return E(()=>{const t=$.filterProps(e);return F(a($,w({class:["v-img",{"v-img--absolute":e.absolute,"v-img--booting":!N.value},c.value,f.value,e.class],style:[{width:ue(e.width==="auto"?g.value:e.width)},d.value,e.style]},t,{aspectRatio:_.value,"aria-label":e.alt,role:e.alt?"img":void 0}),{additional:()=>a(de,null,[a(A,null,null),a(J,null,null),a(G,null,null),a(L,null,null),a(Q,null,null)]),default:n.default}),[[oe("intersect"),{handler:R,options:e.options},null,{once:!0}]])}),{currentSrc:b,image:r,state:i,naturalWidth:g,naturalHeight:S}}});export{be as I,I as M,ye as V,me as m};
