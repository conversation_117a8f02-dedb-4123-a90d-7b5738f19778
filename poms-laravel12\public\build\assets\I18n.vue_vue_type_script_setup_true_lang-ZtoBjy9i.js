import{Y as f,d,Z as m,a as _,g as l,o as r,f as t,b as s,X as g,m as V,c as b,i as C,s as v,t as x,F as I}from"./main-CWjS3hwz.js";import{V as y,a as L,b as h}from"./VList-CUNWEV6C.js";import{V as k}from"./VMenu-ClqezPud.js";const u=(a,n)=>{var c;const e=f();return e?e.proxy&&"$can"in e.proxy?(c=e.proxy)==null?void 0:c.$can(a,n):!0:!1},$=a=>{const n=a.children.some(e=>u(e.action,e.subject));return a.action&&a.subject?u(a.action,a.subject)&&n:n},F=d({__name:"I18n",props:{languages:{},location:{default:"bottom end"}},setup(a){const n=a,{locale:e}=m({useScope:"global"});return(i,c)=>{const p=_("IconBtn");return r(),l(p,null,{default:t(()=>[s(g,{icon:"tabler-language"}),s(k,{activator:"parent",location:n.location,offset:"12px",width:"175"},{default:t(()=>[s(y,{selected:[V(e)],color:"primary"},{default:t(()=>[(r(!0),b(I,null,C(n.languages,o=>(r(),l(L,{key:o.i18nLang,value:o.i18nLang,onClick:B=>e.value=o.i18nLang},{default:t(()=>[s(h,null,{default:t(()=>[v(x(o.label),1)]),_:2},1024)]),_:2},1032,["value","onClick"]))),128))]),_:1},8,["selected"])]),_:1},8,["location"])]),_:1})}}});export{F as _,u as a,$ as c};
