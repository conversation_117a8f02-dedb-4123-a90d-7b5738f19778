const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/AppBarSearch-DwBPVFB5.js","assets/main-CWjS3hwz.js","assets/main-D3k-IUpH.css","assets/vue3-perfect-scrollbar-chdc-gFa.js","assets/VCard-y1UgCbHQ.js","assets/VAvatar-Ds2UYUGl.js","assets/VImg-B7Lnz0wk.js","assets/VImg-DPhT_SK2.css","assets/VAvatar-DDIwAwmR.css","assets/VCardText-DnBTLtJ0.js","assets/VCard-D4JYFFDu.css","assets/VTextField-XbG62wJL.js","assets/forwardRefs-B931MWyl.js","assets/VTextField-B_td_0yy.css","assets/VDivider-DaCbr10A.js","assets/VDivider-BI3KGmL4.css","assets/VList-CUNWEV6C.js","assets/VList-CfjcLfRv.css","assets/VDialog-GhSSCIsG.js","assets/VOverlay-zhJGflkj.js","assets/VOverlay-C6MD-24P.css","assets/VDialog-gkCfFbCR.css","assets/_plugin-vue_export-helper-DlAUqK2U.js","assets/AppBarSearch-BAj2igvq.css"])))=>i.map(i=>d[i]);
import{a1 as Sn,a2 as On,a3 as En,d as An,a4 as Cn,r as Ue,j as Tn,w as Rn,a as In,c as we,o as z,e as J,b as I,l as At,f as A,X as Pe,m as ee,p as kn,s as tt,t as Me,F as Fe,i as We,g as nt,a5 as Ln,a6 as Pn,a7 as Mn}from"./main-CWjS3hwz.js";import{c as Fn,a as Ct,b as Tt,V as Dn}from"./VList-CUNWEV6C.js";import{V as Bn}from"./VCardText-DnBTLtJ0.js";import{V as Vn,a as Nn}from"./VRow-BqKZbSVo.js";import"./VImg-B7Lnz0wk.js";import"./VAvatar-Ds2UYUGl.js";import"./VDivider-DaCbr10A.js";/* empty css              *//*! shepherd.js 13.0.3 */function jn(e){return e instanceof Element}function je(e){return e instanceof HTMLElement}function ne(e){return typeof e=="function"}function Be(e){return typeof e=="string"}function C(e){return e===void 0}class pt{on(t,n,o,i=!1){var r;return C(this.bindings)&&(this.bindings={}),C(this.bindings[t])&&(this.bindings[t]=[]),(r=this.bindings[t])==null||r.push({handler:n,ctx:o,once:i}),this}once(t,n,o){return this.on(t,n,o,!0)}off(t,n){if(C(this.bindings)||C(this.bindings[t]))return this;if(C(n))delete this.bindings[t];else{var o;(o=this.bindings[t])==null||o.forEach((i,r)=>{if(i.handler===n){var s;(s=this.bindings[t])==null||s.splice(r,1)}})}return this}trigger(t,...n){if(!C(this.bindings)&&this.bindings[t]){var o;(o=this.bindings[t])==null||o.forEach((i,r)=>{const{ctx:s,handler:l,once:c}=i,a=s||this;if(l.apply(a,n),c){var f;(f=this.bindings[t])==null||f.splice(r,1)}})}return this}}function R(){return R=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},R.apply(null,arguments)}function Gt(e,t){if(e==null)return{};var n={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(t.includes(o))continue;n[o]=e[o]}return n}const se={defaultMerge:Symbol("deepmerge-ts: default merge"),skip:Symbol("deepmerge-ts: skip")};se.defaultMerge;function Hn(e,t){return t}function Rt(e){return typeof e!="object"||e===null?0:Array.isArray(e)?2:Wn(e)?1:e instanceof Set?3:e instanceof Map?4:5}function $n(e){const t=new Set;for(const n of e)for(const o of[...Object.keys(n),...Object.getOwnPropertySymbols(n)])t.add(o);return t}function Un(e,t){return typeof e=="object"&&Object.prototype.propertyIsEnumerable.call(e,t)}function Zt(e){return{*[Symbol.iterator](){for(const t of e)for(const n of t)yield n}}}const It=new Set(["[object Object]","[object Module]"]);function Wn(e){if(!It.has(Object.prototype.toString.call(e)))return!1;const{constructor:t}=e;if(t===void 0)return!0;const n=t.prototype;return!(n===null||typeof n!="object"||!It.has(Object.prototype.toString.call(n))||!n.hasOwnProperty("isPrototypeOf"))}function zn(e,t,n){const o={};for(const i of $n(e)){const r=[];for(const c of e)Un(c,i)&&r.push(c[i]);if(r.length===0)continue;const s=t.metaDataUpdater(n,{key:i,parents:e}),l=en(r,t,s);l!==se.skip&&(i==="__proto__"?Object.defineProperty(o,i,{value:l,configurable:!0,enumerable:!0,writable:!0}):o[i]=l)}return o}function Kn(e){return e.flat()}function Qn(e){return new Set(Zt(e))}function Xn(e){return new Map(Zt(e))}function Jt(e){return e.at(-1)}var ot=Object.freeze({__proto__:null,mergeArrays:Kn,mergeMaps:Xn,mergeOthers:Jt,mergeRecords:zn,mergeSets:Qn});function mt(...e){return Yn({})(...e)}function Yn(e,t){const n=qn(e,o);function o(...i){return en(i,n,t)}return o}function qn(e,t){var n,o;return{defaultMergeFunctions:ot,mergeFunctions:R({},ot,Object.fromEntries(Object.entries(e).filter(([i,r])=>Object.hasOwn(ot,i)).map(([i,r])=>r===!1?[i,Jt]:[i,r]))),metaDataUpdater:(n=e.metaDataUpdater)!=null?n:Hn,deepmerge:t,useImplicitDefaultMerging:(o=e.enableImplicitDefaultMerging)!=null?o:!1,actions:se}}function en(e,t,n){if(e.length===0)return;if(e.length===1)return it(e,t,n);const o=Rt(e[0]);if(o!==0&&o!==5){for(let i=1;i<e.length;i++)if(Rt(e[i])!==o)return it(e,t,n)}switch(o){case 1:return Gn(e,t,n);case 2:return Zn(e,t,n);case 3:return Jn(e,t,n);case 4:return eo(e,t,n);default:return it(e,t,n)}}function Gn(e,t,n){const o=t.mergeFunctions.mergeRecords(e,t,n);return o===se.defaultMerge||t.useImplicitDefaultMerging&&o===void 0&&t.mergeFunctions.mergeRecords!==t.defaultMergeFunctions.mergeRecords?t.defaultMergeFunctions.mergeRecords(e,t,n):o}function Zn(e,t,n){const o=t.mergeFunctions.mergeArrays(e,t,n);return o===se.defaultMerge||t.useImplicitDefaultMerging&&o===void 0&&t.mergeFunctions.mergeArrays!==t.defaultMergeFunctions.mergeArrays?t.defaultMergeFunctions.mergeArrays(e):o}function Jn(e,t,n){const o=t.mergeFunctions.mergeSets(e,t,n);return o===se.defaultMerge||t.useImplicitDefaultMerging&&o===void 0&&t.mergeFunctions.mergeSets!==t.defaultMergeFunctions.mergeSets?t.defaultMergeFunctions.mergeSets(e):o}function eo(e,t,n){const o=t.mergeFunctions.mergeMaps(e,t,n);return o===se.defaultMerge||t.useImplicitDefaultMerging&&o===void 0&&t.mergeFunctions.mergeMaps!==t.defaultMergeFunctions.mergeMaps?t.defaultMergeFunctions.mergeMaps(e):o}function it(e,t,n){const o=t.mergeFunctions.mergeOthers(e,t,n);return o===se.defaultMerge||t.useImplicitDefaultMerging&&o===void 0&&t.mergeFunctions.mergeOthers!==t.defaultMergeFunctions.mergeOthers?t.defaultMergeFunctions.mergeOthers(e):o}function gt(e){const t=Object.getOwnPropertyNames(e.constructor.prototype);for(let n=0;n<t.length;n++){const o=t[n],i=e[o];o!=="constructor"&&typeof i=="function"&&(e[o]=i.bind(e))}return e}function to(e,t){return n=>{if(e.isOpen()){const o=e.el&&n.currentTarget===e.el;(!C(t)&&n.currentTarget.matches(t)||o)&&e.tour.next()}}}function no(e){const{event:t,selector:n}=e.options.advanceOn||{};if(t){const o=to(e,n);let i=null;if(!C(n)&&(i=document.querySelector(n),!i))return console.error(`No element was found for the selector supplied to advanceOn: ${n}`);i?(i.addEventListener(t,o),e.on("destroy",()=>i.removeEventListener(t,o))):(document.body.addEventListener(t,o,!0),e.on("destroy",()=>document.body.removeEventListener(t,o,!0)))}else return console.error("advanceOn was defined, but no event name was passed.")}class oo{constructor(t){}}class io{constructor(t,n){}}function tn(e){return!Be(e)||e===""?"":e.charAt(e.length-1)!=="-"?`${e}-`:e}function so(e){const t=e.options.attachTo||{},n=Object.assign({},t);if(ne(n.element)&&(n.element=n.element.call(e)),Be(n.element)){try{n.element=document.querySelector(n.element)}catch{}n.element||console.error(`The element for this Shepherd step was not found ${t.element}`)}return n}function nn(e){return e==null?!0:!e.element||!e.on}function on(){let e=Date.now();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,t=>{const n=(e+Math.random()*16)%16|0;return e=Math.floor(e/16),(t=="x"?n:n&3|8).toString(16)})}const Ee=Math.min,ue=Math.max,Qe=Math.round,ze=Math.floor,oe=e=>({x:e,y:e}),ro={left:"right",right:"left",bottom:"top",top:"bottom"},lo={start:"end",end:"start"};function ct(e,t,n){return ue(e,Ee(t,n))}function Ae(e,t){return typeof e=="function"?e(t):e}function he(e){return e.split("-")[0]}function Ge(e){return e.split("-")[1]}function bt(e){return e==="x"?"y":"x"}function yt(e){return e==="y"?"height":"width"}function Ce(e){return["top","bottom"].includes(he(e))?"y":"x"}function wt(e){return bt(Ce(e))}function co(e,t,n){n===void 0&&(n=!1);const o=Ge(e),i=wt(e),r=yt(i);let s=i==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return t.reference[r]>t.floating[r]&&(s=Xe(s)),[s,Xe(s)]}function ao(e){const t=Xe(e);return[at(e),t,at(t)]}function at(e){return e.replace(/start|end/g,t=>lo[t])}function uo(e,t,n){const o=["left","right"],i=["right","left"],r=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return n?t?i:o:t?o:i;case"left":case"right":return t?r:s;default:return[]}}function fo(e,t,n,o){const i=Ge(e);let r=uo(he(e),n==="start",o);return i&&(r=r.map(s=>s+"-"+i),t&&(r=r.concat(r.map(at)))),r}function Xe(e){return e.replace(/left|right|bottom|top/g,t=>ro[t])}function ho(e){return R({top:0,right:0,bottom:0,left:0},e)}function sn(e){return typeof e!="number"?ho(e):{top:e,right:e,bottom:e,left:e}}function Ye(e){const{x:t,y:n,width:o,height:i}=e;return{width:o,height:i,top:n,left:t,right:t+o,bottom:n+i,x:t,y:n}}const po=["mainAxis","crossAxis","fallbackPlacements","fallbackStrategy","fallbackAxisSideDirection","flipAlignment"],mo=["mainAxis","crossAxis","limiter"];function kt(e,t,n){let{reference:o,floating:i}=e;const r=Ce(t),s=wt(t),l=yt(s),c=he(t),a=r==="y",f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,u=o[l]/2-i[l]/2;let h;switch(c){case"top":h={x:f,y:o.y-i.height};break;case"bottom":h={x:f,y:o.y+o.height};break;case"right":h={x:o.x+o.width,y:d};break;case"left":h={x:o.x-i.width,y:d};break;default:h={x:o.x,y:o.y}}switch(Ge(t)){case"start":h[s]-=u*(n&&a?-1:1);break;case"end":h[s]+=u*(n&&a?-1:1);break}return h}const go=async(e,t,n)=>{const{placement:o="bottom",strategy:i="absolute",middleware:r=[],platform:s}=n,l=r.filter(Boolean),c=await(s.isRTL==null?void 0:s.isRTL(t));let a=await s.getElementRects({reference:e,floating:t,strategy:i}),{x:f,y:d}=kt(a,o,c),u=o,h={},m=0;for(let w=0;w<l.length;w++){const{name:b,fn:g}=l[w],{x:_,y:v,data:y,reset:p}=await g({x:f,y:d,initialPlacement:o,placement:u,strategy:i,middlewareData:h,rects:a,platform:s,elements:{reference:e,floating:t}});f=_??f,d=v??d,h=R({},h,{[b]:R({},h[b],y)}),p&&m<=50&&(m++,typeof p=="object"&&(p.placement&&(u=p.placement),p.rects&&(a=p.rects===!0?await s.getElementRects({reference:e,floating:t,strategy:i}):p.rects),{x:f,y:d}=kt(a,u,c)),w=-1)}return{x:f,y:d,placement:u,strategy:i,middlewareData:h}};async function rn(e,t){var n;t===void 0&&(t={});const{x:o,y:i,platform:r,rects:s,elements:l,strategy:c}=e,{boundary:a="clippingAncestors",rootBoundary:f="viewport",elementContext:d="floating",altBoundary:u=!1,padding:h=0}=Ae(t,e),m=sn(h),b=l[u?d==="floating"?"reference":"floating":d],g=Ye(await r.getClippingRect({element:(n=await(r.isElement==null?void 0:r.isElement(b)))==null||n?b:b.contextElement||await(r.getDocumentElement==null?void 0:r.getDocumentElement(l.floating)),boundary:a,rootBoundary:f,strategy:c})),_=d==="floating"?{x:o,y:i,width:s.floating.width,height:s.floating.height}:s.reference,v=await(r.getOffsetParent==null?void 0:r.getOffsetParent(l.floating)),y=await(r.isElement==null?void 0:r.isElement(v))?await(r.getScale==null?void 0:r.getScale(v))||{x:1,y:1}:{x:1,y:1},p=Ye(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:_,offsetParent:v,strategy:c}):_);return{top:(g.top-p.top+m.top)/y.y,bottom:(p.bottom-g.bottom+m.bottom)/y.y,left:(g.left-p.left+m.left)/y.x,right:(p.right-g.right+m.right)/y.x}}const bo=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:i,rects:r,platform:s,elements:l,middlewareData:c}=t,{element:a,padding:f=0}=Ae(e,t)||{};if(a==null)return{};const d=sn(f),u={x:n,y:o},h=wt(i),m=yt(h),w=await s.getDimensions(a),b=h==="y",g=b?"top":"left",_=b?"bottom":"right",v=b?"clientHeight":"clientWidth",y=r.reference[m]+r.reference[h]-u[h]-r.floating[m],p=u[h]-r.reference[h],x=await(s.getOffsetParent==null?void 0:s.getOffsetParent(a));let T=x?x[v]:0;(!T||!await(s.isElement==null?void 0:s.isElement(x)))&&(T=l.floating[v]||r.floating[m]);const q=y/2-p/2,B=T/2-w[m]/2-1,$=Ee(d[g],B),P=Ee(d[_],B),M=$,ce=T-w[m]-P,F=T/2-w[m]/2+q,ge=ct(M,F,ce),V=!c.arrow&&Ge(i)!=null&&F!==ge&&r.reference[m]/2-(F<M?$:P)-w[m]/2<0,be=V?F<M?F-M:F-ce:0;return{[h]:u[h]+be,data:R({[h]:ge,centerOffset:F-ge-be},V&&{alignmentOffset:be}),reset:V}}}),yo=function(t){return t===void 0&&(t={}),{name:"flip",options:t,async fn(n){var o,i;const{placement:r,middlewareData:s,rects:l,initialPlacement:c,platform:a,elements:f}=n,d=Ae(t,n),{mainAxis:u=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:w="bestFit",fallbackAxisSideDirection:b="none",flipAlignment:g=!0}=d,_=Gt(d,po);if((o=s.arrow)!=null&&o.alignmentOffset)return{};const v=he(r),y=Ce(c),p=he(c)===c,x=await(a.isRTL==null?void 0:a.isRTL(f.floating)),T=m||(p||!g?[Xe(c)]:ao(c)),q=b!=="none";!m&&q&&T.push(...fo(c,g,b,x));const B=[c,...T],$=await rn(n,_),P=[];let M=((i=s.flip)==null?void 0:i.overflows)||[];if(u&&P.push($[v]),h){const V=co(r,l,x);P.push($[V[0]],$[V[1]])}if(M=[...M,{placement:r,overflows:P}],!P.every(V=>V<=0)){var ce,F;const V=(((ce=s.flip)==null?void 0:ce.index)||0)+1,be=B[V];if(be)return{data:{index:V,overflows:M},reset:{placement:be}};let Le=(F=M.filter(ye=>ye.overflows[0]<=0).sort((ye,G)=>ye.overflows[1]-G.overflows[1])[0])==null?void 0:F.placement;if(!Le)switch(w){case"bestFit":{var ge;const ye=(ge=M.filter(G=>{if(q){const Z=Ce(G.placement);return Z===y||Z==="y"}return!0}).map(G=>[G.placement,G.overflows.filter(Z=>Z>0).reduce((Z,xn)=>Z+xn,0)]).sort((G,Z)=>G[1]-Z[1])[0])==null?void 0:ge[0];ye&&(Le=ye);break}case"initialPlacement":Le=c;break}if(r!==Le)return{reset:{placement:Le}}}return{}}}},wo=function(t){return t===void 0&&(t={}),{name:"shift",options:t,async fn(n){const{x:o,y:i,placement:r}=n,s=Ae(t,n),{mainAxis:l=!0,crossAxis:c=!1,limiter:a={fn:_=>{let{x:v,y}=_;return{x:v,y}}}}=s,f=Gt(s,mo),d={x:o,y:i},u=await rn(n,f),h=Ce(he(r)),m=bt(h);let w=d[m],b=d[h];if(l){const _=m==="y"?"top":"left",v=m==="y"?"bottom":"right",y=w+u[_],p=w-u[v];w=ct(y,w,p)}if(c){const _=h==="y"?"top":"left",v=h==="y"?"bottom":"right",y=b+u[_],p=b-u[v];b=ct(y,b,p)}const g=a.fn(R({},n,{[m]:w,[h]:b}));return R({},g,{data:{x:g.x-o,y:g.y-i}})}}},_o=function(t){return t===void 0&&(t={}),{options:t,fn(n){const{x:o,y:i,placement:r,rects:s,middlewareData:l}=n,{offset:c=0,mainAxis:a=!0,crossAxis:f=!0}=Ae(t,n),d={x:o,y:i},u=Ce(r),h=bt(u);let m=d[h],w=d[u];const b=Ae(c,n),g=typeof b=="number"?{mainAxis:b,crossAxis:0}:R({mainAxis:0,crossAxis:0},b);if(a){const y=h==="y"?"height":"width",p=s.reference[h]-s.floating[y]+g.mainAxis,x=s.reference[h]+s.reference[y]-g.mainAxis;m<p?m=p:m>x&&(m=x)}if(f){var _,v;const y=h==="y"?"width":"height",p=["top","left"].includes(he(r)),x=s.reference[u]-s.floating[y]+(p&&((_=l.offset)==null?void 0:_[u])||0)+(p?0:g.crossAxis),T=s.reference[u]+s.reference[y]+(p?0:((v=l.offset)==null?void 0:v[u])||0)-(p?g.crossAxis:0);w<x?w=x:w>T&&(w=T)}return{[h]:m,[u]:w}}}};function ke(e){return ln(e)?(e.nodeName||"").toLowerCase():"#document"}function k(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function K(e){var t;return(t=(ln(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function ln(e){return e instanceof Node||e instanceof k(e).Node}function N(e){return e instanceof Element||e instanceof k(e).Element}function U(e){return e instanceof HTMLElement||e instanceof k(e).HTMLElement}function Lt(e){return typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof k(e).ShadowRoot}function He(e){const{overflow:t,overflowX:n,overflowY:o,display:i}=j(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!["inline","contents"].includes(i)}function vo(e){return["table","td","th"].includes(ke(e))}function Ze(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function _t(e){const t=vt(),n=N(e)?j(e):e;return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(o=>(n.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(n.contain||"").includes(o))}function xo(e){let t=ie(e);for(;U(t)&&!Te(t);){if(_t(t))return t;if(Ze(t))return null;t=ie(t)}return null}function vt(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Te(e){return["html","body","#document"].includes(ke(e))}function j(e){return k(e).getComputedStyle(e)}function Je(e){return N(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ie(e){if(ke(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Lt(e)&&e.host||K(e);return Lt(t)?t.host:t}function cn(e){const t=ie(e);return Te(t)?e.ownerDocument?e.ownerDocument.body:e.body:U(t)&&He(t)?t:cn(t)}function Ve(e,t,n){var o;t===void 0&&(t=[]),n===void 0&&(n=!0);const i=cn(e),r=i===((o=e.ownerDocument)==null?void 0:o.body),s=k(i);return r?t.concat(s,s.visualViewport||[],He(i)?i:[],s.frameElement&&n?Ve(s.frameElement):[]):t.concat(i,Ve(i,[],n))}function an(e){const t=j(e);let n=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const i=U(e),r=i?e.offsetWidth:n,s=i?e.offsetHeight:o,l=Qe(n)!==r||Qe(o)!==s;return l&&(n=r,o=s),{width:n,height:o,$:l}}function xt(e){return N(e)?e:e.contextElement}function Se(e){const t=xt(e);if(!U(t))return oe(1);const n=t.getBoundingClientRect(),{width:o,height:i,$:r}=an(t);let s=(r?Qe(n.width):n.width)/o,l=(r?Qe(n.height):n.height)/i;return(!s||!Number.isFinite(s))&&(s=1),(!l||!Number.isFinite(l))&&(l=1),{x:s,y:l}}const So=oe(0);function un(e){const t=k(e);return!vt()||!t.visualViewport?So:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Oo(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==k(e)?!1:t}function pe(e,t,n,o){t===void 0&&(t=!1),n===void 0&&(n=!1);const i=e.getBoundingClientRect(),r=xt(e);let s=oe(1);t&&(o?N(o)&&(s=Se(o)):s=Se(e));const l=Oo(r,n,o)?un(r):oe(0);let c=(i.left+l.x)/s.x,a=(i.top+l.y)/s.y,f=i.width/s.x,d=i.height/s.y;if(r){const u=k(r),h=o&&N(o)?k(o):o;let m=u,w=m.frameElement;for(;w&&o&&h!==m;){const b=Se(w),g=w.getBoundingClientRect(),_=j(w),v=g.left+(w.clientLeft+parseFloat(_.paddingLeft))*b.x,y=g.top+(w.clientTop+parseFloat(_.paddingTop))*b.y;c*=b.x,a*=b.y,f*=b.x,d*=b.y,c+=v,a+=y,m=k(w),w=m.frameElement}}return Ye({width:f,height:d,x:c,y:a})}function Eo(e){let{elements:t,rect:n,offsetParent:o,strategy:i}=e;const r=i==="fixed",s=K(o),l=t?Ze(t.floating):!1;if(o===s||l&&r)return n;let c={scrollLeft:0,scrollTop:0},a=oe(1);const f=oe(0),d=U(o);if((d||!d&&!r)&&((ke(o)!=="body"||He(s))&&(c=Je(o)),U(o))){const u=pe(o);a=Se(o),f.x=u.x+o.clientLeft,f.y=u.y+o.clientTop}return{width:n.width*a.x,height:n.height*a.y,x:n.x*a.x-c.scrollLeft*a.x+f.x,y:n.y*a.y-c.scrollTop*a.y+f.y}}function Ao(e){return Array.from(e.getClientRects())}function fn(e){return pe(K(e)).left+Je(e).scrollLeft}function Co(e){const t=K(e),n=Je(e),o=e.ownerDocument.body,i=ue(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),r=ue(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight);let s=-n.scrollLeft+fn(e);const l=-n.scrollTop;return j(o).direction==="rtl"&&(s+=ue(t.clientWidth,o.clientWidth)-i),{width:i,height:r,x:s,y:l}}function To(e,t){const n=k(e),o=K(e),i=n.visualViewport;let r=o.clientWidth,s=o.clientHeight,l=0,c=0;if(i){r=i.width,s=i.height;const a=vt();(!a||a&&t==="fixed")&&(l=i.offsetLeft,c=i.offsetTop)}return{width:r,height:s,x:l,y:c}}function Ro(e,t){const n=pe(e,!0,t==="fixed"),o=n.top+e.clientTop,i=n.left+e.clientLeft,r=U(e)?Se(e):oe(1),s=e.clientWidth*r.x,l=e.clientHeight*r.y,c=i*r.x,a=o*r.y;return{width:s,height:l,x:c,y:a}}function Pt(e,t,n){let o;if(t==="viewport")o=To(e,n);else if(t==="document")o=Co(K(e));else if(N(t))o=Ro(t,n);else{const i=un(e);o=R({},t,{x:t.x-i.x,y:t.y-i.y})}return Ye(o)}function dn(e,t){const n=ie(e);return n===t||!N(n)||Te(n)?!1:j(n).position==="fixed"||dn(n,t)}function Io(e,t){const n=t.get(e);if(n)return n;let o=Ve(e,[],!1).filter(l=>N(l)&&ke(l)!=="body"),i=null;const r=j(e).position==="fixed";let s=r?ie(e):e;for(;N(s)&&!Te(s);){const l=j(s),c=_t(s);!c&&l.position==="fixed"&&(i=null),(r?!c&&!i:!c&&l.position==="static"&&!!i&&["absolute","fixed"].includes(i.position)||He(s)&&!c&&dn(e,s))?o=o.filter(f=>f!==s):i=l,s=ie(s)}return t.set(e,o),o}function ko(e){let{element:t,boundary:n,rootBoundary:o,strategy:i}=e;const s=[...n==="clippingAncestors"?Ze(t)?[]:Io(t,this._c):[].concat(n),o],l=s[0],c=s.reduce((a,f)=>{const d=Pt(t,f,i);return a.top=ue(d.top,a.top),a.right=Ee(d.right,a.right),a.bottom=Ee(d.bottom,a.bottom),a.left=ue(d.left,a.left),a},Pt(t,l,i));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}}function Lo(e){const{width:t,height:n}=an(e);return{width:t,height:n}}function Po(e,t,n){const o=U(t),i=K(t),r=n==="fixed",s=pe(e,!0,r,t);let l={scrollLeft:0,scrollTop:0};const c=oe(0);if(o||!o&&!r)if((ke(t)!=="body"||He(i))&&(l=Je(t)),o){const d=pe(t,!0,r,t);c.x=d.x+t.clientLeft,c.y=d.y+t.clientTop}else i&&(c.x=fn(i));const a=s.left+l.scrollLeft-c.x,f=s.top+l.scrollTop-c.y;return{x:a,y:f,width:s.width,height:s.height}}function st(e){return j(e).position==="static"}function Mt(e,t){return!U(e)||j(e).position==="fixed"?null:t?t(e):e.offsetParent}function hn(e,t){const n=k(e);if(Ze(e))return n;if(!U(e)){let i=ie(e);for(;i&&!Te(i);){if(N(i)&&!st(i))return i;i=ie(i)}return n}let o=Mt(e,t);for(;o&&vo(o)&&st(o);)o=Mt(o,t);return o&&Te(o)&&st(o)&&!_t(o)?n:o||xo(e)||n}const Mo=async function(t){const n=this.getOffsetParent||hn,o=this.getDimensions,i=await o(t.floating);return{reference:Po(t.reference,await n(t.floating),t.strategy),floating:{x:0,y:0,width:i.width,height:i.height}}};function Fo(e){return j(e).direction==="rtl"}const Do={convertOffsetParentRelativeRectToViewportRelativeRect:Eo,getDocumentElement:K,getClippingRect:ko,getOffsetParent:hn,getElementRects:Mo,getClientRects:Ao,getDimensions:Lo,getScale:Se,isElement:N,isRTL:Fo};function Bo(e,t){let n=null,o;const i=K(e);function r(){var l;clearTimeout(o),(l=n)==null||l.disconnect(),n=null}function s(l,c){l===void 0&&(l=!1),c===void 0&&(c=1),r();const{left:a,top:f,width:d,height:u}=e.getBoundingClientRect();if(l||t(),!d||!u)return;const h=ze(f),m=ze(i.clientWidth-(a+d)),w=ze(i.clientHeight-(f+u)),b=ze(a),_={rootMargin:-h+"px "+-m+"px "+-w+"px "+-b+"px",threshold:ue(0,Ee(1,c))||1};let v=!0;function y(p){const x=p[0].intersectionRatio;if(x!==c){if(!v)return s();x?s(!1,x):o=setTimeout(()=>{s(!1,1e-7)},1e3)}v=!1}try{n=new IntersectionObserver(y,R({},_,{root:i.ownerDocument}))}catch{n=new IntersectionObserver(y,_)}n.observe(e)}return s(!0),r}function Vo(e,t,n,o){o===void 0&&(o={});const{ancestorScroll:i=!0,ancestorResize:r=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:c=!1}=o,a=xt(e),f=i||r?[...a?Ve(a):[],...Ve(t)]:[];f.forEach(g=>{i&&g.addEventListener("scroll",n,{passive:!0}),r&&g.addEventListener("resize",n)});const d=a&&l?Bo(a,n):null;let u=-1,h=null;s&&(h=new ResizeObserver(g=>{let[_]=g;_&&_.target===a&&h&&(h.unobserve(t),cancelAnimationFrame(u),u=requestAnimationFrame(()=>{var v;(v=h)==null||v.observe(t)})),n()}),a&&!c&&h.observe(a),h.observe(t));let m,w=c?pe(e):null;c&&b();function b(){const g=pe(e);w&&(g.x!==w.x||g.y!==w.y||g.width!==w.width||g.height!==w.height)&&n(),w=g,m=requestAnimationFrame(b)}return n(),()=>{var g;f.forEach(_=>{i&&_.removeEventListener("scroll",n),r&&_.removeEventListener("resize",n)}),d==null||d(),(g=h)==null||g.disconnect(),h=null,c&&cancelAnimationFrame(m)}}const No=wo,jo=yo,Ho=bo,$o=_o,Uo=(e,t,n)=>{const o=new Map,i=R({platform:Do},n),r=R({},i.platform,{_c:o});return go(e,t,R({},i,{platform:r}))};function Wo(e){e.cleanup&&e.cleanup();const t=e._getResolvedAttachToOptions();let n=t.element;const o=qo(t,e),i=nn(t);return i&&(n=document.body,e.shepherdElementComponent.getElement().classList.add("shepherd-centered")),e.cleanup=Vo(n,e.el,()=>{if(!e.el){e.cleanup==null||e.cleanup();return}Qo(n,e,o,i)}),e.target=t.element,o}function zo(e,t){return{floatingUIOptions:mt(e.floatingUIOptions||{},t.floatingUIOptions||{})}}function Ko(e){e.cleanup&&e.cleanup(),e.cleanup=null}function Qo(e,t,n,o){return Uo(e,t.el,n).then(Xo(t,o)).then(i=>new Promise(r=>{setTimeout(()=>r(i),300)})).then(i=>{i!=null&&i.el&&i.el.focus({preventScroll:!0})})}function Xo(e,t){return({x:n,y:o,placement:i,middlewareData:r})=>(e.el&&(t?Object.assign(e.el.style,{position:"fixed",left:"50%",top:"50%",transform:"translate(-50%, -50%)"}):Object.assign(e.el.style,{position:"absolute",left:`${n}px`,top:`${o}px`}),e.el.dataset.popperPlacement=i,Yo(e.el,r)),e)}function Yo(e,t){const n=e.querySelector(".shepherd-arrow");if(je(n)&&t.arrow){const{x:o,y:i}=t.arrow;Object.assign(n.style,{left:o!=null?`${o}px`:"",top:i!=null?`${i}px`:""})}}function qo(e,t){const n={strategy:"absolute"};n.middleware=[];const o=Go(t);if(!nn(e)){if(n.middleware.push(jo(),No({limiter:$o(),crossAxis:!0})),o){var r,s;const l=(e==null||(r=e.on)==null?void 0:r.includes("-start"))||(e==null||(s=e.on)==null?void 0:s.includes("-end"));n.middleware.push(Ho({element:o,padding:l?4:0}))}n.placement=e.on}return mt(t.options.floatingUIOptions||{},n)}function Go(e){return e.options.arrow&&e.el?e.el.querySelector(".shepherd-arrow"):!1}function L(){}function Zo(e,t){for(const n in t)e[n]=t[n];return e}function pn(e){return e()}function Ft(){return Object.create(null)}function $e(e){e.forEach(pn)}function St(e){return typeof e=="function"}function Q(e,t){return e!=e?t==t:e!==t||e&&typeof e=="object"||typeof e=="function"}function Jo(e){return Object.keys(e).length===0}function Re(e,t){e.appendChild(t)}function H(e,t,n){e.insertBefore(t,n||null)}function D(e){e.parentNode&&e.parentNode.removeChild(e)}function ei(e,t){for(let n=0;n<e.length;n+=1)e[n]&&e[n].d(t)}function W(e){return document.createElement(e)}function Dt(e){return document.createElementNS("http://www.w3.org/2000/svg",e)}function mn(e){return document.createTextNode(e)}function qe(){return mn(" ")}function ti(){return mn("")}function et(e,t,n,o){return e.addEventListener(t,n,o),()=>e.removeEventListener(t,n,o)}function O(e,t,n){n==null?e.removeAttribute(t):e.getAttribute(t)!==n&&e.setAttribute(t,n)}const ni=["width","height"];function Bt(e,t){const n=Object.getOwnPropertyDescriptors(e.__proto__);for(const o in t)t[o]==null?e.removeAttribute(o):o==="style"?e.style.cssText=t[o]:o==="__value"?e.value=e[o]=t[o]:n[o]&&n[o].set&&ni.indexOf(o)===-1?e[o]=t[o]:O(e,o,t[o])}function oi(e){return Array.from(e.childNodes)}function _e(e,t,n){e.classList.toggle(t,!!n)}let Ne;function De(e){Ne=e}function gn(){if(!Ne)throw new Error("Function called outside component initialization");return Ne}function ii(e){gn().$$.on_mount.push(e)}function Ot(e){gn().$$.after_update.push(e)}const xe=[],Ie=[];let Oe=[];const Vt=[],si=Promise.resolve();let ut=!1;function ri(){ut||(ut=!0,si.then(bn))}function ft(e){Oe.push(e)}const rt=new Set;let ve=0;function bn(){if(ve!==0)return;const e=Ne;do{try{for(;ve<xe.length;){const t=xe[ve];ve++,De(t),li(t.$$)}}catch(t){throw xe.length=0,ve=0,t}for(De(null),xe.length=0,ve=0;Ie.length;)Ie.pop()();for(let t=0;t<Oe.length;t+=1){const n=Oe[t];rt.has(n)||(rt.add(n),n())}Oe.length=0}while(xe.length);for(;Vt.length;)Vt.pop()();ut=!1,rt.clear(),De(e)}function li(e){if(e.fragment!==null){e.update(),$e(e.before_update);const t=e.dirty;e.dirty=[-1],e.fragment&&e.fragment.p(e.ctx,t),e.after_update.forEach(ft)}}function ci(e){const t=[],n=[];Oe.forEach(o=>e.indexOf(o)===-1?t.push(o):n.push(o)),n.forEach(o=>o()),Oe=t}const Ke=new Set;let ae;function fe(){ae={r:0,c:[],p:ae}}function de(){ae.r||$e(ae.c),ae=ae.p}function S(e,t){e&&e.i&&(Ke.delete(e),e.i(t))}function E(e,t,n,o){if(e&&e.o){if(Ke.has(e))return;Ke.add(e),ae.c.push(()=>{Ke.delete(e),o&&(n&&e.d(1),o())}),e.o(t)}else o&&o()}function Nt(e){return(e==null?void 0:e.length)!==void 0?e:Array.from(e)}function ai(e,t){const n={},o={},i={$$scope:1};let r=e.length;for(;r--;){const s=e[r],l=t[r];if(l){for(const c in s)c in l||(o[c]=1);for(const c in l)i[c]||(n[c]=l[c],i[c]=1);e[r]=l}else for(const c in s)i[c]=1}for(const s in o)s in n||(n[s]=void 0);return n}function me(e){e&&e.c()}function re(e,t,n){const{fragment:o,after_update:i}=e.$$;o&&o.m(t,n),ft(()=>{const r=e.$$.on_mount.map(pn).filter(St);e.$$.on_destroy?e.$$.on_destroy.push(...r):$e(r),e.$$.on_mount=[]}),i.forEach(ft)}function le(e,t){const n=e.$$;n.fragment!==null&&(ci(n.after_update),$e(n.on_destroy),n.fragment&&n.fragment.d(t),n.on_destroy=n.fragment=null,n.ctx=[])}function ui(e,t){e.$$.dirty[0]===-1&&(xe.push(e),ri(),e.$$.dirty.fill(0)),e.$$.dirty[t/31|0]|=1<<t%31}function X(e,t,n,o,i,r,s=null,l=[-1]){const c=Ne;De(e);const a=e.$$={fragment:null,ctx:[],props:r,update:L,not_equal:i,bound:Ft(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(t.context||(c?c.$$.context:[])),callbacks:Ft(),dirty:l,skip_bound:!1,root:t.target||c.$$.root};s&&s(a.root);let f=!1;if(a.ctx=n?n(e,t.props||{},(d,u,...h)=>{const m=h.length?h[0]:u;return a.ctx&&i(a.ctx[d],a.ctx[d]=m)&&(!a.skip_bound&&a.bound[d]&&a.bound[d](m),f&&ui(e,d)),u}):[],a.update(),f=!0,$e(a.before_update),a.fragment=o?o(a.ctx):!1,t.target){if(t.hydrate){const d=oi(t.target);a.fragment&&a.fragment.l(d),d.forEach(D)}else a.fragment&&a.fragment.c();t.intro&&S(e.$$.fragment),re(e,t.target,t.anchor),bn()}De(c)}class Y{constructor(){this.$$=void 0,this.$$set=void 0}$destroy(){le(this,1),this.$destroy=L}$on(t,n){if(!St(n))return L;const o=this.$$.callbacks[t]||(this.$$.callbacks[t]=[]);return o.push(n),()=>{const i=o.indexOf(n);i!==-1&&o.splice(i,1)}}$set(t){this.$$set&&!Jo(t)&&(this.$$.skip_bound=!0,this.$$set(t),this.$$.skip_bound=!1)}}const fi="4";typeof window<"u"&&(window.__svelte||(window.__svelte={v:new Set})).v.add(fi);function di(e){let t,n,o,i,r;return{c(){t=W("button"),O(t,"aria-label",n=e[3]?e[3]:null),O(t,"class",o=`${e[1]||""} shepherd-button ${e[4]?"shepherd-button-secondary":""}`),t.disabled=e[2],O(t,"tabindex","0"),O(t,"type","button")},m(s,l){H(s,t,l),t.innerHTML=e[5],i||(r=et(t,"click",function(){St(e[0])&&e[0].apply(this,arguments)}),i=!0)},p(s,[l]){e=s,l&32&&(t.innerHTML=e[5]),l&8&&n!==(n=e[3]?e[3]:null)&&O(t,"aria-label",n),l&18&&o!==(o=`${e[1]||""} shepherd-button ${e[4]?"shepherd-button-secondary":""}`)&&O(t,"class",o),l&4&&(t.disabled=e[2])},i:L,o:L,d(s){s&&D(t),i=!1,r()}}}function hi(e,t,n){let{config:o,step:i}=t,r,s,l,c,a,f;function d(u){return ne(u)?u=u.call(i):u}return e.$$set=u=>{"config"in u&&n(6,o=u.config),"step"in u&&n(7,i=u.step)},e.$$.update=()=>{e.$$.dirty&192&&(n(0,r=o.action?o.action.bind(i.tour):null),n(1,s=o.classes),n(2,l=o.disabled?d(o.disabled):!1),n(3,c=o.label?d(o.label):null),n(4,a=o.secondary),n(5,f=o.text?d(o.text):null))},[r,s,l,c,a,f,o,i]}class pi extends Y{constructor(t){super(),X(this,t,hi,di,Q,{config:6,step:7})}}function jt(e,t,n){const o=e.slice();return o[2]=t[n],o}function Ht(e){let t,n,o=Nt(e[1]),i=[];for(let s=0;s<o.length;s+=1)i[s]=$t(jt(e,o,s));const r=s=>E(i[s],1,1,()=>{i[s]=null});return{c(){for(let s=0;s<i.length;s+=1)i[s].c();t=ti()},m(s,l){for(let c=0;c<i.length;c+=1)i[c]&&i[c].m(s,l);H(s,t,l),n=!0},p(s,l){if(l&3){o=Nt(s[1]);let c;for(c=0;c<o.length;c+=1){const a=jt(s,o,c);i[c]?(i[c].p(a,l),S(i[c],1)):(i[c]=$t(a),i[c].c(),S(i[c],1),i[c].m(t.parentNode,t))}for(fe(),c=o.length;c<i.length;c+=1)r(c);de()}},i(s){if(!n){for(let l=0;l<o.length;l+=1)S(i[l]);n=!0}},o(s){i=i.filter(Boolean);for(let l=0;l<i.length;l+=1)E(i[l]);n=!1},d(s){s&&D(t),ei(i,s)}}}function $t(e){let t,n;return t=new pi({props:{config:e[2],step:e[0]}}),{c(){me(t.$$.fragment)},m(o,i){re(t,o,i),n=!0},p(o,i){const r={};i&2&&(r.config=o[2]),i&1&&(r.step=o[0]),t.$set(r)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){E(t.$$.fragment,o),n=!1},d(o){le(t,o)}}}function mi(e){let t,n,o=e[1]&&Ht(e);return{c(){t=W("footer"),o&&o.c(),O(t,"class","shepherd-footer")},m(i,r){H(i,t,r),o&&o.m(t,null),n=!0},p(i,[r]){i[1]?o?(o.p(i,r),r&2&&S(o,1)):(o=Ht(i),o.c(),S(o,1),o.m(t,null)):o&&(fe(),E(o,1,1,()=>{o=null}),de())},i(i){n||(S(o),n=!0)},o(i){E(o),n=!1},d(i){i&&D(t),o&&o.d()}}}function gi(e,t,n){let o,{step:i}=t;return e.$$set=r=>{"step"in r&&n(0,i=r.step)},e.$$.update=()=>{e.$$.dirty&1&&n(1,o=i.options.buttons)},[i,o]}class bi extends Y{constructor(t){super(),X(this,t,gi,mi,Q,{step:0})}}function yi(e){let t,n,o,i,r;return{c(){t=W("button"),n=W("span"),n.textContent="×",O(n,"aria-hidden","true"),O(t,"aria-label",o=e[0].label?e[0].label:"Close Tour"),O(t,"class","shepherd-cancel-icon"),O(t,"type","button")},m(s,l){H(s,t,l),Re(t,n),i||(r=et(t,"click",e[1]),i=!0)},p(s,[l]){l&1&&o!==(o=s[0].label?s[0].label:"Close Tour")&&O(t,"aria-label",o)},i:L,o:L,d(s){s&&D(t),i=!1,r()}}}function wi(e,t,n){let{cancelIcon:o,step:i}=t;const r=s=>{s.preventDefault(),i.cancel()};return e.$$set=s=>{"cancelIcon"in s&&n(0,o=s.cancelIcon),"step"in s&&n(2,i=s.step)},[o,r,i]}class _i extends Y{constructor(t){super(),X(this,t,wi,yi,Q,{cancelIcon:0,step:2})}}function vi(e){let t;return{c(){t=W("h3"),O(t,"id",e[1]),O(t,"class","shepherd-title")},m(n,o){H(n,t,o),e[3](t)},p(n,[o]){o&2&&O(t,"id",n[1])},i:L,o:L,d(n){n&&D(t),e[3](null)}}}function xi(e,t,n){let{labelId:o,element:i,title:r}=t;Ot(()=>{ne(r)&&n(2,r=r()),n(0,i.innerHTML=r,i)});function s(l){Ie[l?"unshift":"push"](()=>{i=l,n(0,i)})}return e.$$set=l=>{"labelId"in l&&n(1,o=l.labelId),"element"in l&&n(0,i=l.element),"title"in l&&n(2,r=l.title)},[i,o,r,s]}class Si extends Y{constructor(t){super(),X(this,t,xi,vi,Q,{labelId:1,element:0,title:2})}}function Ut(e){let t,n;return t=new Si({props:{labelId:e[0],title:e[2]}}),{c(){me(t.$$.fragment)},m(o,i){re(t,o,i),n=!0},p(o,i){const r={};i&1&&(r.labelId=o[0]),i&4&&(r.title=o[2]),t.$set(r)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){E(t.$$.fragment,o),n=!1},d(o){le(t,o)}}}function Wt(e){let t,n;return t=new _i({props:{cancelIcon:e[3],step:e[1]}}),{c(){me(t.$$.fragment)},m(o,i){re(t,o,i),n=!0},p(o,i){const r={};i&8&&(r.cancelIcon=o[3]),i&2&&(r.step=o[1]),t.$set(r)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){E(t.$$.fragment,o),n=!1},d(o){le(t,o)}}}function Oi(e){let t,n,o,i=e[2]&&Ut(e),r=e[3]&&e[3].enabled&&Wt(e);return{c(){t=W("header"),i&&i.c(),n=qe(),r&&r.c(),O(t,"class","shepherd-header")},m(s,l){H(s,t,l),i&&i.m(t,null),Re(t,n),r&&r.m(t,null),o=!0},p(s,[l]){s[2]?i?(i.p(s,l),l&4&&S(i,1)):(i=Ut(s),i.c(),S(i,1),i.m(t,n)):i&&(fe(),E(i,1,1,()=>{i=null}),de()),s[3]&&s[3].enabled?r?(r.p(s,l),l&8&&S(r,1)):(r=Wt(s),r.c(),S(r,1),r.m(t,null)):r&&(fe(),E(r,1,1,()=>{r=null}),de())},i(s){o||(S(i),S(r),o=!0)},o(s){E(i),E(r),o=!1},d(s){s&&D(t),i&&i.d(),r&&r.d()}}}function Ei(e,t,n){let{labelId:o,step:i}=t,r,s;return e.$$set=l=>{"labelId"in l&&n(0,o=l.labelId),"step"in l&&n(1,i=l.step)},e.$$.update=()=>{e.$$.dirty&2&&(n(2,r=i.options.title),n(3,s=i.options.cancelIcon))},[o,i,r,s]}class Ai extends Y{constructor(t){super(),X(this,t,Ei,Oi,Q,{labelId:0,step:1})}}function Ci(e){let t;return{c(){t=W("div"),O(t,"class","shepherd-text"),O(t,"id",e[1])},m(n,o){H(n,t,o),e[3](t)},p(n,[o]){o&2&&O(t,"id",n[1])},i:L,o:L,d(n){n&&D(t),e[3](null)}}}function Ti(e,t,n){let{descriptionId:o,element:i,step:r}=t;Ot(()=>{let{text:l}=r.options;ne(l)&&(l=l.call(r)),je(l)?i.appendChild(l):n(0,i.innerHTML=l,i)});function s(l){Ie[l?"unshift":"push"](()=>{i=l,n(0,i)})}return e.$$set=l=>{"descriptionId"in l&&n(1,o=l.descriptionId),"element"in l&&n(0,i=l.element),"step"in l&&n(2,r=l.step)},[i,o,r,s]}class Ri extends Y{constructor(t){super(),X(this,t,Ti,Ci,Q,{descriptionId:1,element:0,step:2})}}function zt(e){let t,n;return t=new Ai({props:{labelId:e[1],step:e[2]}}),{c(){me(t.$$.fragment)},m(o,i){re(t,o,i),n=!0},p(o,i){const r={};i&2&&(r.labelId=o[1]),i&4&&(r.step=o[2]),t.$set(r)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){E(t.$$.fragment,o),n=!1},d(o){le(t,o)}}}function Kt(e){let t,n;return t=new Ri({props:{descriptionId:e[0],step:e[2]}}),{c(){me(t.$$.fragment)},m(o,i){re(t,o,i),n=!0},p(o,i){const r={};i&1&&(r.descriptionId=o[0]),i&4&&(r.step=o[2]),t.$set(r)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){E(t.$$.fragment,o),n=!1},d(o){le(t,o)}}}function Qt(e){let t,n;return t=new bi({props:{step:e[2]}}),{c(){me(t.$$.fragment)},m(o,i){re(t,o,i),n=!0},p(o,i){const r={};i&4&&(r.step=o[2]),t.$set(r)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){E(t.$$.fragment,o),n=!1},d(o){le(t,o)}}}function Ii(e){let t,n=!C(e[2].options.title)||e[2].options.cancelIcon&&e[2].options.cancelIcon.enabled,o,i=!C(e[2].options.text),r,s=Array.isArray(e[2].options.buttons)&&e[2].options.buttons.length,l,c=n&&zt(e),a=i&&Kt(e),f=s&&Qt(e);return{c(){t=W("div"),c&&c.c(),o=qe(),a&&a.c(),r=qe(),f&&f.c(),O(t,"class","shepherd-content")},m(d,u){H(d,t,u),c&&c.m(t,null),Re(t,o),a&&a.m(t,null),Re(t,r),f&&f.m(t,null),l=!0},p(d,[u]){u&4&&(n=!C(d[2].options.title)||d[2].options.cancelIcon&&d[2].options.cancelIcon.enabled),n?c?(c.p(d,u),u&4&&S(c,1)):(c=zt(d),c.c(),S(c,1),c.m(t,o)):c&&(fe(),E(c,1,1,()=>{c=null}),de()),u&4&&(i=!C(d[2].options.text)),i?a?(a.p(d,u),u&4&&S(a,1)):(a=Kt(d),a.c(),S(a,1),a.m(t,r)):a&&(fe(),E(a,1,1,()=>{a=null}),de()),u&4&&(s=Array.isArray(d[2].options.buttons)&&d[2].options.buttons.length),s?f?(f.p(d,u),u&4&&S(f,1)):(f=Qt(d),f.c(),S(f,1),f.m(t,null)):f&&(fe(),E(f,1,1,()=>{f=null}),de())},i(d){l||(S(c),S(a),S(f),l=!0)},o(d){E(c),E(a),E(f),l=!1},d(d){d&&D(t),c&&c.d(),a&&a.d(),f&&f.d()}}}function ki(e,t,n){let{descriptionId:o,labelId:i,step:r}=t;return e.$$set=s=>{"descriptionId"in s&&n(0,o=s.descriptionId),"labelId"in s&&n(1,i=s.labelId),"step"in s&&n(2,r=s.step)},[o,i,r]}class Li extends Y{constructor(t){super(),X(this,t,ki,Ii,Q,{descriptionId:0,labelId:1,step:2})}}function Xt(e){let t;return{c(){t=W("div"),O(t,"class","shepherd-arrow"),O(t,"data-popper-arrow","")},m(n,o){H(n,t,o)},d(n){n&&D(t)}}}function Pi(e){let t,n,o,i,r,s,l,c,a=e[4].options.arrow&&e[4].options.attachTo&&e[4].options.attachTo.element&&e[4].options.attachTo.on&&Xt();o=new Li({props:{descriptionId:e[2],labelId:e[3],step:e[4]}});let f=[{"aria-describedby":i=C(e[4].options.text)?null:e[2]},{"aria-labelledby":r=e[4].options.title?e[3]:null},e[1],{role:"dialog"},{tabindex:"0"}],d={};for(let u=0;u<f.length;u+=1)d=Zo(d,f[u]);return{c(){t=W("div"),a&&a.c(),n=qe(),me(o.$$.fragment),Bt(t,d),_e(t,"shepherd-has-cancel-icon",e[5]),_e(t,"shepherd-has-title",e[6]),_e(t,"shepherd-element",!0)},m(u,h){H(u,t,h),a&&a.m(t,null),Re(t,n),re(o,t,null),e[13](t),s=!0,l||(c=et(t,"keydown",e[7]),l=!0)},p(u,[h]){u[4].options.arrow&&u[4].options.attachTo&&u[4].options.attachTo.element&&u[4].options.attachTo.on?a||(a=Xt(),a.c(),a.m(t,n)):a&&(a.d(1),a=null);const m={};h&4&&(m.descriptionId=u[2]),h&8&&(m.labelId=u[3]),h&16&&(m.step=u[4]),o.$set(m),Bt(t,d=ai(f,[(!s||h&20&&i!==(i=C(u[4].options.text)?null:u[2]))&&{"aria-describedby":i},(!s||h&24&&r!==(r=u[4].options.title?u[3]:null))&&{"aria-labelledby":r},h&2&&u[1],{role:"dialog"},{tabindex:"0"}])),_e(t,"shepherd-has-cancel-icon",u[5]),_e(t,"shepherd-has-title",u[6]),_e(t,"shepherd-element",!0)},i(u){s||(S(o.$$.fragment,u),s=!0)},o(u){E(o.$$.fragment,u),s=!1},d(u){u&&D(t),a&&a.d(),le(o),e[13](null),l=!1,c()}}}const Mi=9,Fi=27,Di=37,Bi=39;function Yt(e){return e.split(" ").filter(t=>!!t.length)}function Vi(e,t,n){let{classPrefix:o,element:i,descriptionId:r,firstFocusableElement:s,focusableElements:l,labelId:c,lastFocusableElement:a,step:f,dataStepId:d}=t,u,h,m;const w=()=>i;ii(()=>{n(1,d={[`data-${o}shepherd-step-id`]:f.id}),n(9,l=i.querySelectorAll('a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), [tabindex="0"]')),n(8,s=l[0]),n(10,a=l[l.length-1])}),Ot(()=>{m!==f.options.classes&&b()});function b(){g(m),m=f.options.classes,_(m)}function g(p){if(Be(p)){const x=Yt(p);x.length&&i.classList.remove(...x)}}function _(p){if(Be(p)){const x=Yt(p);x.length&&i.classList.add(...x)}}const v=p=>{const{tour:x}=f;switch(p.keyCode){case Mi:if(l.length===0){p.preventDefault();break}p.shiftKey?(document.activeElement===s||document.activeElement.classList.contains("shepherd-element"))&&(p.preventDefault(),a.focus()):document.activeElement===a&&(p.preventDefault(),s.focus());break;case Fi:x.options.exitOnEsc&&(p.preventDefault(),p.stopPropagation(),f.cancel());break;case Di:x.options.keyboardNavigation&&(p.preventDefault(),p.stopPropagation(),x.back());break;case Bi:x.options.keyboardNavigation&&(p.preventDefault(),p.stopPropagation(),x.next());break}};function y(p){Ie[p?"unshift":"push"](()=>{i=p,n(0,i)})}return e.$$set=p=>{"classPrefix"in p&&n(11,o=p.classPrefix),"element"in p&&n(0,i=p.element),"descriptionId"in p&&n(2,r=p.descriptionId),"firstFocusableElement"in p&&n(8,s=p.firstFocusableElement),"focusableElements"in p&&n(9,l=p.focusableElements),"labelId"in p&&n(3,c=p.labelId),"lastFocusableElement"in p&&n(10,a=p.lastFocusableElement),"step"in p&&n(4,f=p.step),"dataStepId"in p&&n(1,d=p.dataStepId)},e.$$.update=()=>{e.$$.dirty&16&&(n(5,u=f.options&&f.options.cancelIcon&&f.options.cancelIcon.enabled),n(6,h=f.options&&f.options.title))},[i,d,r,c,f,u,h,v,s,l,a,o,w,y]}class Ni extends Y{constructor(t){super(),X(this,t,Vi,Pi,Q,{classPrefix:11,element:0,descriptionId:2,firstFocusableElement:8,focusableElements:9,labelId:3,lastFocusableElement:10,step:4,dataStepId:1,getElement:12})}get getElement(){return this.$$.ctx[12]}}class dt extends pt{constructor(t,n={}){return super(),this._resolvedAttachTo=void 0,this.classPrefix=void 0,this.el=void 0,this.target=void 0,this.tour=void 0,this.tour=t,this.classPrefix=this.tour.options?tn(this.tour.options.classPrefix):"",this.styles=t.styles,this._resolvedAttachTo=null,gt(this),this._setOptions(n),this}cancel(){this.tour.cancel(),this.trigger("cancel")}complete(){this.tour.complete(),this.trigger("complete")}destroy(){Ko(this),je(this.el)&&(this.el.remove(),this.el=null),this._updateStepTargetOnHide(),this.trigger("destroy")}getTour(){return this.tour}hide(){var t;(t=this.tour.modal)==null||t.hide(),this.trigger("before-hide"),this.el&&(this.el.hidden=!0),this._updateStepTargetOnHide(),this.trigger("hide")}_resolveAttachToOptions(){return this._resolvedAttachTo=so(this),this._resolvedAttachTo}_getResolvedAttachToOptions(){return this._resolvedAttachTo===null?this._resolveAttachToOptions():this._resolvedAttachTo}isOpen(){return!!(this.el&&!this.el.hidden)}show(){return ne(this.options.beforeShowPromise)?Promise.resolve(this.options.beforeShowPromise()).then(()=>this._show()):Promise.resolve(this._show())}updateStepOptions(t){Object.assign(this.options,t),this.shepherdElementComponent&&this.shepherdElementComponent.$set({step:this})}getElement(){return this.el}getTarget(){return this.target}_createTooltipContent(){const t=`${this.id}-description`,n=`${this.id}-label`;return this.shepherdElementComponent=new Ni({target:this.tour.options.stepsContainer||document.body,props:{classPrefix:this.classPrefix,descriptionId:t,labelId:n,step:this,styles:this.styles}}),this.shepherdElementComponent.getElement()}_scrollTo(t){const{element:n}=this._getResolvedAttachToOptions();ne(this.options.scrollToHandler)?this.options.scrollToHandler(n):jn(n)&&typeof n.scrollIntoView=="function"&&n.scrollIntoView(t)}_getClassOptions(t){const n=this.tour&&this.tour.options&&this.tour.options.defaultStepOptions,o=t.classes?t.classes:"",i=n&&n.classes?n.classes:"",r=[...o.split(" "),...i.split(" ")],s=new Set(r);return Array.from(s).join(" ").trim()}_setOptions(t={}){let n=this.tour&&this.tour.options&&this.tour.options.defaultStepOptions;n=mt({},n||{}),this.options=Object.assign({arrow:!0},n,t,zo(n,t));const{when:o}=this.options;this.options.classes=this._getClassOptions(t),this.destroy(),this.id=this.options.id||`step-${on()}`,o&&Object.keys(o).forEach(i=>{this.on(i,o[i],this)})}_setupElements(){C(this.el)||this.destroy(),this.el=this._createTooltipContent(),this.options.advanceOn&&no(this),Wo(this)}_show(){var t;this.trigger("before-show"),this._resolveAttachToOptions(),this._setupElements(),this.tour.modal||this.tour.setupModal(),(t=this.tour.modal)==null||t.setupForStep(this),this._styleTargetElementForStep(this),this.el&&(this.el.hidden=!1),this.options.scrollTo&&setTimeout(()=>{this._scrollTo(this.options.scrollTo)}),this.el&&(this.el.hidden=!1);const n=this.shepherdElementComponent.getElement(),o=this.target||document.body;o.classList.add(`${this.classPrefix}shepherd-enabled`),o.classList.add(`${this.classPrefix}shepherd-target`),n.classList.add("shepherd-enabled"),this.trigger("show")}_styleTargetElementForStep(t){const n=t.target;n&&(t.options.highlightClass&&n.classList.add(t.options.highlightClass),n.classList.remove("shepherd-target-click-disabled"),t.options.canClickTarget===!1&&n.classList.add("shepherd-target-click-disabled"))}_updateStepTargetOnHide(){const t=this.target||document.body;this.options.highlightClass&&t.classList.remove(this.options.highlightClass),t.classList.remove("shepherd-target-click-disabled",`${this.classPrefix}shepherd-enabled`,`${this.classPrefix}shepherd-target`)}}function ji(e){if(e){const{steps:t}=e;t.forEach(n=>{n.options&&n.options.canClickTarget===!1&&n.options.attachTo&&je(n.target)&&n.target.classList.remove("shepherd-target-click-disabled")})}}function Hi({width:e,height:t,x:n=0,y:o=0,r:i=0}){const{innerWidth:r,innerHeight:s}=window,{topLeft:l=0,topRight:c=0,bottomRight:a=0,bottomLeft:f=0}=typeof i=="number"?{topLeft:i,topRight:i,bottomRight:i,bottomLeft:i}:i;return`M${r},${s}H0V0H${r}V${s}ZM${n+l},${o}a${l},${l},0,0,0-${l},${l}V${t+o-f}a${f},${f},0,0,0,${f},${f}H${e+n-a}a${a},${a},0,0,0,${a}-${a}V${o+c}a${c},${c},0,0,0-${c}-${c}Z`}function $i(e){let t,n,o,i,r;return{c(){t=Dt("svg"),n=Dt("path"),O(n,"d",e[2]),O(t,"class",o=`${e[1]?"shepherd-modal-is-visible":""} shepherd-modal-overlay-container`)},m(s,l){H(s,t,l),Re(t,n),e[11](t),i||(r=et(t,"touchmove",e[3]),i=!0)},p(s,[l]){l&4&&O(n,"d",s[2]),l&2&&o!==(o=`${s[1]?"shepherd-modal-is-visible":""} shepherd-modal-overlay-container`)&&O(t,"class",o)},i:L,o:L,d(s){s&&D(t),e[11](null),i=!1,r()}}}function yn(e){if(!e)return null;const n=e instanceof HTMLElement&&window.getComputedStyle(e).overflowY;return n!=="hidden"&&n!=="visible"&&e.scrollHeight>=e.clientHeight?e:yn(e.parentElement)}function Ui(e){let t={top:0,left:0};if(!e)return t;let n=e.ownerDocument.defaultView;for(;n!==window.top;){var o;const s=(o=n)==null?void 0:o.frameElement;if(s){var i,r;const l=s.getBoundingClientRect();t.top+=l.top+((i=l.scrollTop)!=null?i:0),t.left+=l.left+((r=l.scrollLeft)!=null?r:0)}n=n.parent}return t}function Wi(e,t){const n=e.getBoundingClientRect();let o=n.y||n.top,i=n.bottom||o+n.height;if(t){const s=t.getBoundingClientRect(),l=s.y||s.top,c=s.bottom||l+s.height;o=Math.max(o,l),i=Math.min(i,c)}const r=Math.max(i-o,0);return{y:o,height:r}}function zi(e,t,n){let{element:o,openingProperties:i}=t,r=!1,s,l;a();const c=()=>o;function a(){n(4,i={width:0,height:0,x:0,y:0,r:0})}function f(){n(1,r=!1),g()}function d(y=0,p=0,x=0,T=0,q,B){if(B){const{y:$,height:P}=Wi(B,q),{x:M,width:ce,left:F}=B.getBoundingClientRect();n(4,i={width:ce+y*2,height:P+y*2,x:(M||F)+x-y,y:$+T-y,r:p})}else a()}function u(y){g(),y.tour.options.useModalOverlay?(_(y),h()):f()}function h(){n(1,r=!0)}const m=y=>{y.preventDefault()},w=y=>{y.stopPropagation()};function b(){window.addEventListener("touchmove",m,{passive:!1})}function g(){s&&(cancelAnimationFrame(s),s=void 0),window.removeEventListener("touchmove",m,{passive:!1})}function _(y){const{modalOverlayOpeningPadding:p,modalOverlayOpeningRadius:x,modalOverlayOpeningXOffset:T=0,modalOverlayOpeningYOffset:q=0}=y.options,B=Ui(y.target),$=yn(y.target),P=()=>{s=void 0,d(p,x,T+B.left,q+B.top,$,y.target),s=requestAnimationFrame(P)};P(),b()}function v(y){Ie[y?"unshift":"push"](()=>{o=y,n(0,o)})}return e.$$set=y=>{"element"in y&&n(0,o=y.element),"openingProperties"in y&&n(4,i=y.openingProperties)},e.$$.update=()=>{e.$$.dirty&16&&n(2,l=Hi(i))},[o,r,l,w,i,c,a,f,d,u,h,v]}class Ki extends Y{constructor(t){super(),X(this,t,zi,$i,Q,{element:0,openingProperties:4,getElement:5,closeModalOpening:6,hide:7,positionModal:8,setupForStep:9,show:10})}get getElement(){return this.$$.ctx[5]}get closeModalOpening(){return this.$$.ctx[6]}get hide(){return this.$$.ctx[7]}get positionModal(){return this.$$.ctx[8]}get setupForStep(){return this.$$.ctx[9]}get show(){return this.$$.ctx[10]}}class Qi extends pt{constructor(){super(),this.activeTour=void 0,gt(this)}}class Xi extends pt{constructor(t={}){super(),this.trackedEvents=["active","cancel","complete","show"],this.classPrefix=void 0,this.currentStep=void 0,this.focusedElBeforeOpen=void 0,this.id=void 0,this.modal=void 0,this.options=void 0,this.steps=void 0,gt(this);const n={exitOnEsc:!0,keyboardNavigation:!0};return this.options=Object.assign({},n,t),this.classPrefix=tn(this.options.classPrefix),this.steps=[],this.addSteps(this.options.steps),["active","cancel","complete","inactive","show","start"].map(i=>{(r=>{this.on(r,s=>{s=s||{},s.tour=this,te.trigger(r,s)})})(i)}),this._setTourID(t.id),this}addStep(t,n){let o=t;return o instanceof dt?o.tour=this:o=new dt(this,o),C(n)?this.steps.push(o):this.steps.splice(n,0,o),o}addSteps(t){return Array.isArray(t)&&t.forEach(n=>{this.addStep(n)}),this}back(){const t=this.steps.indexOf(this.currentStep);this.show(t-1,!1)}async cancel(){if(this.options.confirmCancel){const t=this.options.confirmCancelMessage||"Are you sure you want to stop the tour?";let n;ne(this.options.confirmCancel)?n=await this.options.confirmCancel():n=window.confirm(t),n&&this._done("cancel")}else this._done("cancel")}complete(){this._done("complete")}getById(t){return this.steps.find(n=>n.id===t)}getCurrentStep(){return this.currentStep}hide(){const t=this.getCurrentStep();if(t)return t.hide()}isActive(){return te.activeTour===this}next(){const t=this.steps.indexOf(this.currentStep);t===this.steps.length-1?this.complete():this.show(t+1,!0)}removeStep(t){const n=this.getCurrentStep();this.steps.some((o,i)=>{if(o.id===t)return o.isOpen()&&o.hide(),o.destroy(),this.steps.splice(i,1),!0}),n&&n.id===t&&(this.currentStep=void 0,this.steps.length?this.show(0):this.cancel())}show(t=0,n=!0){const o=Be(t)?this.getById(t):this.steps[t];o&&(this._updateStateBeforeShow(),ne(o.options.showOn)&&!o.options.showOn()?this._skipStep(o,n):(this.trigger("show",{step:o,previous:this.currentStep}),this.currentStep=o,o.show()))}async start(){this.trigger("start"),this.focusedElBeforeOpen=document.activeElement,this.currentStep=null,this.setupModal(),this._setupActiveTour(),this.next()}_done(t){const n=this.steps.indexOf(this.currentStep);if(Array.isArray(this.steps)&&this.steps.forEach(o=>o.destroy()),ji(this),this.trigger(t,{index:n}),te.activeTour=null,this.trigger("inactive",{tour:this}),this.modal&&this.modal.hide(),(t==="cancel"||t==="complete")&&this.modal){const o=document.querySelector(".shepherd-modal-overlay-container");o&&(o.remove(),this.modal=null)}je(this.focusedElBeforeOpen)&&this.focusedElBeforeOpen.focus()}_setupActiveTour(){this.trigger("active",{tour:this}),te.activeTour=this}setupModal(){this.modal=new Ki({target:this.options.modalContainer||document.body,props:{styles:this.styles}})}_skipStep(t,n){const o=this.steps.indexOf(t);if(o===this.steps.length-1)this.complete();else{const i=n?o+1:o-1;this.show(i,n)}}_updateStateBeforeShow(){this.currentStep&&this.currentStep.hide(),this.isActive()||this._setupActiveTour()}_setTourID(t){const n=this.options.tourName||"tour",o=t||on();this.id=`${n}--${o}`}}const te=new Qi,wn=typeof window>"u";te.Step=wn?oo:dt;te.Tour=wn?io:Xi;const Yi=/#/g,qi=/&/g,Gi=/\//g,Zi=/=/g,Et=/\+/g,Ji=/%5e/gi,es=/%60/gi,ts=/%7c/gi,ns=/%20/gi;function os(e){return encodeURI(""+e).replace(ts,"|")}function ht(e){return os(typeof e=="string"?e:JSON.stringify(e)).replace(Et,"%2B").replace(ns,"+").replace(Yi,"%23").replace(qi,"%26").replace(es,"`").replace(Ji,"^").replace(Gi,"%2F")}function lt(e){return ht(e).replace(Zi,"%3D")}function _n(e=""){try{return decodeURIComponent(""+e)}catch{return""+e}}function is(e){return _n(e.replace(Et," "))}function ss(e){return _n(e.replace(Et," "))}function rs(e=""){const t=Object.create(null);e[0]==="?"&&(e=e.slice(1));for(const n of e.split("&")){const o=n.match(/([^=]+)=?(.*)/)||[];if(o.length<2)continue;const i=is(o[1]);if(i==="__proto__"||i==="constructor")continue;const r=ss(o[2]||"");t[i]===void 0?t[i]=r:Array.isArray(t[i])?t[i].push(r):t[i]=[t[i],r]}return t}function ls(e,t){return(typeof t=="number"||typeof t=="boolean")&&(t=String(t)),t?Array.isArray(t)?t.map(n=>`${lt(e)}=${ht(n)}`).join("&"):`${lt(e)}=${ht(t)}`:lt(e)}function cs(e){return Object.keys(e).filter(t=>e[t]!==void 0).map(t=>ls(t,e[t])).filter(Boolean).join("&")}const as=/^[\s\w\0+.-]{2,}:([/\\]{1,2})/,us=/^[\s\w\0+.-]{2,}:([/\\]{2})?/,fs=/^([/\\]\s*){2,}[^/\\]/;function ds(e,t={}){return typeof t=="boolean"&&(t={acceptRelative:t}),t.strict?as.test(e):us.test(e)||(t.acceptRelative?fs.test(e):!1)}function hs(e,t){const n=ps(e),o={...rs(n.search),...t};return n.search=cs(o),ms(n)}const vn=Symbol.for("ufo:protocolRelative");function ps(e="",t){const n=e.match(/^[\s\0]*(blob:|data:|javascript:|vbscript:)(.*)/i);if(n){const[,d,u=""]=n;return{protocol:d.toLowerCase(),pathname:u,href:d+u,auth:"",host:"",search:"",hash:""}}if(!ds(e,{acceptRelative:!0}))return qt(e);const[,o="",i,r=""]=e.replace(/\\/g,"/").match(/^[\s\0]*([\w+.-]{2,}:)?\/\/([^/@]+@)?(.*)/)||[];let[,s="",l=""]=r.match(/([^#/?]*)(.*)?/)||[];o==="file:"&&(l=l.replace(/\/(?=[A-Za-z]:)/,""));const{pathname:c,search:a,hash:f}=qt(l);return{protocol:o.toLowerCase(),auth:i?i.slice(0,Math.max(0,i.length-1)):"",host:s,pathname:c,search:a,hash:f,[vn]:!o}}function qt(e=""){const[t="",n="",o=""]=(e.match(/([^#?]*)(\?[^#]*)?(#.*)?/)||[]).splice(1);return{pathname:t,search:n,hash:o}}function ms(e){const t=e.pathname||"",n=e.search?(e.search.startsWith("?")?"":"?")+e.search:"",o=e.hash||"",i=e.auth?e.auth+"@":"",r=e.host||"";return(e.protocol||e[vn]?(e.protocol||"")+"//":"")+i+r+t+n+o}const gs=Sn({baseUrl:"/api",fetchOptions:{headers:{Accept:"application/json"}},options:{refetch:!0,async beforeFetch({options:e}){const t=En("accessToken").value;return t&&(e.headers={...e.headers,Authorization:`Bearer ${t}`}),{options:e}},afterFetch(e){const{data:t,response:n}=e;let o=null;try{o=On(t)}catch(i){console.error(i)}return{data:o,response:n}}}}),bs={class:"custom-letter-spacing text-disabled text-uppercase py-2 px-4 mb-0",style:{"font-size":"0.75rem","line-height":"0.875rem"}},ys={class:"mt-9"},ws=["onClick"],Ts=An({inheritAttrs:!1,__name:"NavSearchBar",setup(e){const t=Cn(),n=Ue(!1),o=Ue(!1),i=[{title:"Popular Searches",content:[{icon:"tabler-chart-bar",title:"Analytics",url:{name:"dashboards-analytics"}},{icon:"tabler-chart-donut-3",title:"CRM",url:{name:"dashboards-crm"}},{icon:"tabler-shopping-cart",title:"eCommerce",url:{name:"dashboards-ecommerce"}},{icon:"tabler-truck",title:"Logistics",url:{name:"dashboards-logistics"}}]},{title:"Apps & Pages",content:[{icon:"tabler-calendar",title:"Calendar",url:{name:"apps-calendar"}},{icon:"tabler-lock",title:"Roles & Permissions",url:{name:"apps-roles"}},{icon:"tabler-settings",title:"Account Settings",url:{name:"pages-account-settings-tab",params:{tab:"account"}}},{icon:"tabler-copy",title:"Dialog Examples",url:{name:"pages-dialog-examples"}}]},{title:"User Interface",content:[{icon:"tabler-typography",title:"Typography",url:{name:"pages-typography"}},{icon:"tabler-menu-2",title:"Accordion",url:{name:"components-expansion-panel"}},{icon:"tabler-info-triangle",title:"Alert",url:{name:"components-alert"}},{icon:"tabler-checkbox",title:"Cards",url:{name:"pages-cards-card-basic"}}]},{title:"Forms & Tables",content:[{icon:"tabler-circle-dot",title:"Radio",url:{name:"forms-radio"}},{icon:"tabler-file-invoice",title:"Form Layouts",url:{name:"forms-form-layouts"}},{icon:"tabler-table",title:"Table",url:{name:"tables-data-table"}},{icon:"tabler-edit",title:"Editor",url:{name:"forms-editors"}}]}],r=[{title:"Analytics",icon:"tabler-chart-bar",url:{name:"dashboards-analytics"}},{title:"CRM",icon:"tabler-chart-donut-3",url:{name:"dashboards-crm"}},{title:"eCommerce",icon:"tabler-shopping-cart",url:{name:"dashboards-ecommerce"}}],s=Ue(""),l=Tn(),c=Ue([]);Rn(s,async()=>{o.value=!0;const{data:h}=await gs(hs("/app-bar/search",{q:s.value}));c.value=h.value,setTimeout(()=>{o.value=!1},500)});const f=()=>{n.value=!1,s.value=""},d=h=>{l.push(h.url),f()},u=Pn(()=>Mn(()=>import("./AppBarSearch-DwBPVFB5.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23])));return(h,m)=>{const w=In("IconBtn");return z(),we(Fe,null,[J("div",kn({class:"d-flex align-center cursor-pointer"},h.$attrs,{style:{"user-select":"none"},onClick:m[2]||(m[2]=b=>n.value=!ee(n))}),[I(w,{onClick:m[0]||(m[0]=b=>{var g;return(g=ee(te).activeTour)==null?void 0:g.cancel()})},{default:A(()=>[I(Pe,{icon:"tabler-search"})]),_:1}),ee(t).appContentLayoutNav==="vertical"?(z(),we("span",{key:0,class:"d-none d-md-flex align-center text-disabled ms-2",onClick:m[1]||(m[1]=b=>{var g;return(g=ee(te).activeTour)==null?void 0:g.cancel()})},m[5]||(m[5]=[J("span",{class:"me-2"},"Search",-1),J("span",{class:"meta-key"},"⌘K",-1)]))):At("",!0)],16),I(ee(u),{"is-dialog-visible":ee(n),"onUpdate:isDialogVisible":m[3]||(m[3]=b=>Ln(n)?n.value=b:null),"search-results":ee(c),"is-loading":ee(o),onSearch:m[4]||(m[4]=b=>s.value=b)},{suggestions:A(()=>[I(Bn,{class:"app-bar-search-suggestions pa-12"},{default:A(()=>[i?(z(),nt(Vn,{key:0},{default:A(()=>[(z(),we(Fe,null,We(i,b=>I(Nn,{key:b.title,cols:"12",sm:"6"},{default:A(()=>[J("p",bs,Me(b.title),1),I(Dn,{class:"card-list"},{default:A(()=>[(z(!0),we(Fe,null,We(b.content,g=>(z(),nt(Ct,{key:g.title,class:"app-bar-search-suggestion mx-4 mt-2",onClick:_=>d(g)},{prepend:A(()=>[I(Pe,{icon:g.icon,size:"20",class:"me-n1"},null,8,["icon"])]),default:A(()=>[I(Tt,null,{default:A(()=>[tt(Me(g.title),1)]),_:2},1024)]),_:2},1032,["onClick"]))),128))]),_:2},1024)]),_:2},1024)),64))]),_:1})):At("",!0)]),_:1})]),noDataSuggestion:A(()=>[J("div",ys,[m[6]||(m[6]=J("span",{class:"d-flex justify-center text-disabled mb-2"},"Try searching for",-1)),(z(),we(Fe,null,We(r,b=>J("h6",{key:b.title,class:"app-bar-search-suggestion text-h6 font-weight-regular cursor-pointer py-2 px-4",onClick:g=>d(b)},[I(Pe,{size:"20",icon:b.icon,class:"me-2"},null,8,["icon"]),J("span",null,Me(b.title),1)],8,ws)),64))])]),searchResult:A(({item:b})=>[I(Fn,{class:"text-disabled custom-letter-spacing font-weight-regular ps-4"},{default:A(()=>[tt(Me(b.title),1)]),_:2},1024),(z(!0),we(Fe,null,We(b.children,g=>(z(),nt(Ct,{key:g.title,to:g.url,onClick:f},{prepend:A(()=>[I(Pe,{size:"20",icon:g.icon,class:"me-n1"},null,8,["icon"])]),append:A(()=>[I(Pe,{size:"20",icon:"tabler-corner-down-left",class:"enter-icon flip-in-rtl"})]),default:A(()=>[I(Tt,null,{default:A(()=>[tt(Me(g.title),1)]),_:2},1024)]),_:2},1032,["to"]))),128))]),_:1},8,["is-dialog-visible","search-results","is-loading"])],64)}}});export{Ts as default};
