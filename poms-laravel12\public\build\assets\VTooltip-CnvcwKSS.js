import{u as h,V as u,m as p}from"./VOverlay-zhJGflkj.js";import{f as O}from"./forwardRefs-B931MWyl.js";import{a8 as x,a9 as T,aa as I,ab as R,W as d,r as k,V as n,p as v,ac as w,b as A,ad as B}from"./main-CWjS3hwz.js";const C=T({id:String,interactive:Boolean,text:String,...B(p({closeOnBack:!1,location:"end",locationStrategy:"connected",eager:!0,minWidth:0,offset:10,openOnClick:!1,openOnHover:!0,origin:"auto",scrim:!1,scrollStrategy:"reposition",transition:null}),["absolute","persistent"])},"VTooltip"),H=x()({name:"VTooltip",props:C(),emits:{"update:modelValue":t=>!0},setup(t,m){let{slots:e}=m;const i=I(t,"modelValue"),{scopeId:f}=h(),g=R(),r=d(()=>t.id||`v-tooltip-${g}`),l=k(),V=n(()=>t.location.split(" ").length>1?t.location:t.location+" center"),P=n(()=>t.origin==="auto"||t.origin==="overlap"||t.origin.split(" ").length>1||t.location.split(" ").length>1?t.origin:t.origin+" center"),b=d(()=>t.transition!=null?t.transition:i.value?"scale-transition":"fade-transition"),y=n(()=>v({"aria-describedby":r.value},t.activatorProps));return w(()=>{const S=u.filterProps(t);return A(u,v({ref:l,class:["v-tooltip",{"v-tooltip--interactive":t.interactive},t.class],style:t.style,id:r.value},S,{modelValue:i.value,"onUpdate:modelValue":a=>i.value=a,transition:b.value,absolute:!0,location:V.value,origin:P.value,persistent:!0,role:"tooltip",activatorProps:y.value,_disableGlobalStack:!0},f),{activator:e.activator,default:function(){var c;for(var a=arguments.length,s=new Array(a),o=0;o<a;o++)s[o]=arguments[o];return((c=e.default)==null?void 0:c.call(e,...s))??t.text}})}),O({},l)}});export{H as V};
