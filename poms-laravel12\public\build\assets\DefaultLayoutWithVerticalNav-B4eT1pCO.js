import{d as w,J as M,T as D,r as A,K as Z,k as E,u as F,w as S,a as H,g as v,o as s,h as u,v as R,m as e,f as c,e as f,y as b,b as y,n as r,H as V,t as T,I as k,p as h,c as L,F as z,i as q,L as x,M as K,j as ee,N as ae,l as O,q as B,s as G,O as U,x as j,P as $,Q as te,R as ie,U as se,V as ne,W as oe,z as le,A as re,X as ce,E as W}from"./main-CWjS3hwz.js";import ve from"./Footer-Dy0E_UHM.js";import{_ as pe}from"./NavbarThemeSwitcher.vue_vue_type_script_setup_true_lang-CyquagOA.js";import{_ as de}from"./UserProfile.vue_vue_type_script_setup_true_lang-DeyIGWJQ.js";import{c as ue,a as J,_ as me}from"./I18n.vue_vue_type_script_setup_true_lang-ZtoBjy9i.js";import{C as fe}from"./vue3-perfect-scrollbar-chdc-gFa.js";import{V as ge}from"./VNodeRenderer-DunzcDYp.js";import{_ as Q}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{V as he}from"./VSpacer-CAYcyQHy.js";import"./VTooltip-CnvcwKSS.js";import"./VOverlay-zhJGflkj.js";import"./forwardRefs-B931MWyl.js";import"./VImg-B7Lnz0wk.js";import"./VMenu-ClqezPud.js";import"./VList-CUNWEV6C.js";import"./VAvatar-Ds2UYUGl.js";import"./VDivider-DaCbr10A.js";import"./VBadge-7xlT8rLw.js";/* empty css              */const ye=w({name:"TransitionExpand",setup(_,{slots:l}){const d=t=>{const N=getComputedStyle(t).width;t.style.width=N,t.style.position="absolute",t.style.visibility="hidden",t.style.height="auto";const g=getComputedStyle(t).height;t.style.width="",t.style.position="",t.style.visibility="",t.style.height="0px",getComputedStyle(t).height,requestAnimationFrame(()=>{t.style.height=g})},i=t=>{t.style.height="auto"},n=t=>{const N=getComputedStyle(t).height;t.style.height=N,getComputedStyle(t).height,requestAnimationFrame(()=>{t.style.height="0px"})};return()=>M(M(D),{name:"expand",onEnter:d,onAfterEnter:i,onLeave:n},()=>{var t;return(t=l.default)==null?void 0:t.call(l)})}}),Ne=Q(ye,[["__scopeId","data-v-bc460233"]]),be={class:"nav-header"},_e={class:"header-action"},Ce=w({__name:"VerticalNav",props:{tag:{default:"aside"},navItems:{},isOverlayNavActive:{type:Boolean},toggleIsOverlayNavActive:{}},setup(_){const l=_,d=A(),i=Z(d);x(K,i);const n=E(),t=a=>"heading"in a?Be:"children"in a?$e:X,N=F();S(()=>N.name,()=>{l.toggleIsOverlayNavActive(!1)});const g=A(!1),p=a=>g.value=a,C=a=>{g.value=a.target.scrollTop>0},I=n.isVerticalNavMini(i);return(a,o)=>{const m=H("RouterLink");return s(),v(u(l.tag),{ref_key:"refNav",ref:d,"data-allow-mismatch":"",class:R(["layout-vertical-nav",[{"overlay-nav":e(n).isLessThanOverlayNavBreakpoint,hovered:e(i),visible:a.isOverlayNavActive,scrolled:e(g)}]])},{default:c(()=>[f("div",be,[b(a.$slots,"nav-header",{},()=>[y(m,{to:"/",class:"app-logo app-title-wrapper"},{default:c(()=>[y(e(ge),{nodes:e(r).app.logo},null,8,["nodes"]),y(D,{name:"vertical-nav-app-title"},{default:c(()=>[V(f("h1",{class:"app-logo-title"},T(e(r).app.title),513),[[k,!e(I)]])]),_:1})]),_:1}),f("div",_e,[V((s(),v(u(e(r).app.iconRenderer||"div"),h({class:["d-none nav-unpin",e(n).isVerticalNavCollapsed&&"d-lg-block"]},e(r).icons.verticalNavUnPinned,{onClick:o[0]||(o[0]=P=>e(n).isVerticalNavCollapsed=!e(n).isVerticalNavCollapsed)}),null,16,["class"])),[[k,e(n).isVerticalNavCollapsed]]),V((s(),v(u(e(r).app.iconRenderer||"div"),h({class:["d-none nav-pin",!e(n).isVerticalNavCollapsed&&"d-lg-block"]},e(r).icons.verticalNavPinned,{onClick:o[1]||(o[1]=P=>e(n).isVerticalNavCollapsed=!e(n).isVerticalNavCollapsed)}),null,16,["class"])),[[k,!e(n).isVerticalNavCollapsed]]),(s(),v(u(e(r).app.iconRenderer||"div"),h({class:"d-lg-none"},e(r).icons.close,{onClick:o[2]||(o[2]=P=>a.toggleIsOverlayNavActive(!1))}),null,16))])],!0)]),b(a.$slots,"before-nav-items",{},()=>[o[3]||(o[3]=f("div",{class:"vertical-nav-items-shadow"},null,-1))],!0),b(a.$slots,"nav-items",{updateIsVerticalNavScrolled:p},()=>[(s(),v(e(fe),{key:e(n).isAppRTL,tag:"ul",class:"nav-items",options:{wheelPropagation:!1},onPsScrollY:C},{default:c(()=>[(s(!0),L(z,null,q(a.navItems,(P,Y)=>(s(),v(u(t(P)),{key:Y,item:P},null,8,["item"]))),128))]),_:1}))],!0),b(a.$slots,"after-nav-items",{},void 0,!0)]),_:3},8,["class"])}}}),Ve=Q(Ce,[["__scopeId","data-v-ef7c8c96"]]),ke={class:"nav-group-children"},$e=w({name:"VerticalNavGroup",__name:"VerticalNavGroup",props:{item:{}},setup(_){const l=_,d=F(),i=ee(),n=E(),t=n.isVerticalNavMini(),N=ae(K,A(!1)),g=A(!1),p=A(!1),C=a=>a.some(o=>{let m=$.value.includes(o.title);return"children"in o&&(m=C(o.children)||m),m}),I=a=>{a.forEach(o=>{"children"in o&&I(o.children),$.value=$.value.filter(m=>m!==o.title)})};return S(()=>d.path,()=>{const a=j(l.item.children,i);p.value=a&&!n.isVerticalNavMini(N).value,g.value=a},{immediate:!0}),S(p,a=>{const o=$.value.indexOf(l.item.title);a&&o===-1?$.value.push(l.item.title):!a&&o!==-1&&($.value.splice(o,1),I(l.item.children))},{immediate:!0}),S($,a=>{if(a.at(-1)===l.item.title)return;const m=j(l.item.children,i);m||C(l.item.children)||(p.value=m,g.value=m)},{deep:!0}),S(n.isVerticalNavMini(N),a=>{p.value=a?!1:g.value}),(a,o)=>e(ue)(a.item)?(s(),L("li",{key:0,class:R(["nav-group",[{active:e(g),open:e(p),disabled:a.item.disable}]])},[f("div",{class:"nav-group-label",onClick:o[0]||(o[0]=m=>p.value=!e(p))},[(s(),v(u(e(r).app.iconRenderer||"div"),h(a.item.icon||e(r).verticalNav.defaultNavItemIconProps,{class:"nav-item-icon"}),null,16)),(s(),v(u(U),{name:"transition-slide-x"},{default:c(()=>[V((s(),v(u(e(r).app.i18n.enable?"i18n-t":"span"),h(e(B)(a.item.title,"span"),{key:"title",class:"nav-item-title"}),{default:c(()=>[G(T(a.item.title),1)]),_:1},16)),[[k,!e(t)]]),a.item.badgeContent?V((s(),v(u(e(r).app.i18n.enable?"i18n-t":"span"),h({key:0},e(B)(a.item.badgeContent,"span"),{key:"badge",class:["nav-item-badge",a.item.badgeClass]}),{default:c(()=>[G(T(a.item.badgeContent),1)]),_:1},16,["class"])),[[k,!e(t)]]):O("",!0),V((s(),v(u(e(r).app.iconRenderer||"div"),h(e(r).icons.chevronRight,{key:"arrow",class:"nav-group-arrow"}),null,16)),[[k,!e(t)]])]),_:1}))]),y(e(Ne),null,{default:c(()=>[V(f("ul",ke,[(s(!0),L(z,null,q(a.item.children,m=>(s(),v(u("children"in m?"VerticalNavGroup":e(X)),{key:m.title,item:m},null,8,["item"]))),128))],512),[[k,e(p)]])]),_:1})],2)):O("",!0)}}),Ae={class:"layout-content-wrapper"},we={class:"navbar-content-container"},Ie={class:"layout-page-content"},Se={class:"page-content-container"},Re={class:"layout-footer"},Te={class:"footer-content-container"},Le=w({__name:"VerticalNavLayout",props:{navItems:{},verticalNavAttrs:{default:()=>({})}},setup(_){const l=_,{width:d}=te(),i=E(),n=A(!1),t=A(!1),N=ie(n);se(n,t),S(d,()=>{!i.isLessThanOverlayNavBreakpoint&&t.value&&(t.value=!1)});const g=ne(()=>{const p=oe(l,"verticalNavAttrs"),{wrapper:C,wrapperProps:I,...a}=p.value;return{verticalNavWrapper:C,verticalNavWrapperProps:I,additionalVerticalNavAttrs:a}});return(p,C)=>(s(),L("div",{class:R(["layout-wrapper",e(i)._layoutClasses]),"data-allow-mismatch":""},[(s(),v(u(e(g).verticalNavWrapper?e(g).verticalNavWrapper:"div"),h(e(g).verticalNavWrapperProps,{class:"vertical-nav-wrapper"}),{default:c(()=>[y(e(Ve),h({"is-overlay-nav-active":e(n),"toggle-is-overlay-nav-active":e(N),"nav-items":l.navItems},{...e(g).additionalVerticalNavAttrs}),{"nav-header":c(()=>[b(p.$slots,"vertical-nav-header")]),"before-nav-items":c(()=>[b(p.$slots,"before-vertical-nav-items")]),_:3},16,["is-overlay-nav-active","toggle-is-overlay-nav-active","nav-items"])]),_:3},16)),f("div",Ae,[f("header",{class:R(["layout-navbar",[{"navbar-blur":e(i).isNavbarBlurEnabled}]])},[f("div",we,[b(p.$slots,"navbar",{toggleVerticalOverlayNavActive:e(N)})])],2),f("main",Ie,[f("div",Se,[b(p.$slots,"default")])]),f("footer",Re,[f("div",Te,[b(p.$slots,"footer")])])]),f("div",{class:R(["layout-overlay",[{visible:e(t)}]]),onClick:C[0]||(C[0]=()=>{t.value=!e(t)})},null,2)],2))}}),X=w({__name:"VerticalNavLink",props:{item:{}},setup(_){const d=E().isVerticalNavMini();return(i,n)=>e(J)(i.item.action,i.item.subject)?(s(),L("li",{key:0,class:R(["nav-link",{disabled:i.item.disable}])},[(s(),v(u(i.item.to?"RouterLink":"a"),h(e(re)(i.item),{class:{"router-link-active router-link-exact-active":e(le)(i.item,i.$router)}}),{default:c(()=>[(s(),v(u(e(r).app.iconRenderer||"div"),h(i.item.icon||e(r).verticalNav.defaultNavItemIconProps,{class:"nav-item-icon"}),null,16)),y(U,{name:"transition-slide-x"},{default:c(()=>[V((s(),v(u(e(r).app.i18n.enable?"i18n-t":"span"),h({key:"title",class:"nav-item-title"},e(B)(i.item.title,"span")),{default:c(()=>[G(T(i.item.title),1)]),_:1},16)),[[k,!e(d)]]),i.item.badgeContent?V((s(),v(u(e(r).app.i18n.enable?"i18n-t":"span"),h({key:"badge",class:["nav-item-badge",i.item.badgeClass]},e(B)(i.item.badgeContent,"span")),{default:c(()=>[G(T(i.item.badgeContent),1)]),_:1},16,["class"])),[[k,!e(d)]]):O("",!0)]),_:1})]),_:1},16,["class"]))],2)):O("",!0)}}),Oe={key:0,class:"nav-section-title"},Pe={class:"title-wrapper"},Be=w({__name:"VerticalNavSectionTitle",props:{item:{}},setup(_){const d=E().isVerticalNavMini();return(i,n)=>e(J)(i.item.action,i.item.subject)?(s(),L("li",Oe,[f("div",Pe,[y(D,{name:"vertical-nav-section-title",mode:"out-in"},{default:c(()=>[(s(),v(u(e(d)?e(r).app.iconRenderer:e(r).app.i18n.enable?"i18n-t":"span"),h({key:e(d),class:e(d)?"placeholder-icon":"title-text"},{...e(r).icons.sectionTitlePlaceholder,...e(B)(i.item.heading,"span")}),{default:c(()=>[G(T(e(d)?null:i.item.heading),1)]),_:1},16,["class"]))]),_:1})])])):O("",!0)}}),Ge=[{title:"Dashboard",to:{name:"root"},icon:{icon:"tabler-dashboard"}},{title:"Reports",to:{name:"reports"},icon:{icon:"tabler-file-text"}},{title:"SLA Dashboard",to:{name:"sla-dashboard"},icon:{icon:"tabler-target"}},{title:"Technical Dashboard",to:{name:"tech-dashboard"},icon:{icon:"tabler-cpu"}},{title:"Administrator",to:{name:"admin"},icon:{icon:"tabler-settings"}}],Ee={class:"d-flex h-100 align-center"},ia=w({__name:"DefaultLayoutWithVerticalNav",setup(_){return(l,d)=>{const i=H("IconBtn");return s(),v(e(Le),{"nav-items":e(Ge)},{navbar:c(({toggleVerticalOverlayNavActive:n})=>{var t;return[f("div",Ee,[y(i,{id:"vertical-nav-toggle-btn",class:"ms-n3 d-lg-none",onClick:N=>n(!0)},{default:c(()=>[y(ce,{size:"26",icon:"tabler-menu-2"})]),_:2},1032,["onClick"]),y(pe),y(he),e(W).app.i18n.enable&&((t=e(W).app.i18n.langConfig)!=null&&t.length)?(s(),v(me,{key:0,languages:e(W).app.i18n.langConfig},null,8,["languages"])):O("",!0),y(de)])]}),footer:c(()=>[y(ve)]),default:c(()=>[b(l.$slots,"default")]),_:3},8,["nav-items"])}}});export{ia as default};
