import{d as T,V as w,a as A,g as h,o as c,f as s,b as t,p as I,X as g,H as b,s as _,t as m,m as p,I as V,c as y,F as B,i as U,l as x,e as u,$,v as M,a0 as P,r as E}from"./main-CWjS3hwz.js";import{C as D}from"./vue3-perfect-scrollbar-chdc-gFa.js";import{V as L}from"./VBadge-7xlT8rLw.js";import{V as F}from"./VMenu-ClqezPud.js";import{V as Y,a as j,b as H}from"./VCard-y1UgCbHQ.js";import{V as O}from"./VChip-DuBqIAai.js";import{V as W}from"./VTooltip-CnvcwKSS.js";import{V as C}from"./VDivider-DaCbr10A.js";import{V as X,a as R,b as q}from"./VList-CUNWEV6C.js";import{V as G}from"./VAvatar-Ds2UYUGl.js";import{V as J}from"./VImg-B7Lnz0wk.js";import{V as K}from"./VSpacer-CAYcyQHy.js";import{V as Q}from"./VCardText-DnBTLtJ0.js";import"./VOverlay-zhJGflkj.js";import"./forwardRefs-B931MWyl.js";/* empty css              */const Z=f=>f?f.split(" ").map(o=>o.charAt(0).toUpperCase()).join(""):"",ee={class:"d-flex align-start gap-3"},te={key:0},ae={class:"text-sm font-weight-medium mb-1"},se={class:"text-body-2 mb-2",style:{"letter-spacing":"0.4px !important","line-height":"18px"}},oe={class:"text-sm text-disabled mb-0",style:{"letter-spacing":"0.4px !important","line-height":"18px"}},ie={class:"d-flex flex-column align-end"},le=T({__name:"Notifications",props:{notifications:{},badgeProps:{default:void 0},location:{default:"bottom end"}},emits:["read","unread","remove","click:notification"],setup(f,{emit:n}){const o=f,d=n,v=w(()=>o.notifications.some(a=>a.isSeen===!1)),k=()=>{const a=o.notifications.map(l=>l.id);v.value?d("read",a):d("unread",a)},i=w(()=>o.notifications.filter(a=>a.isSeen===!1).length),r=(a,l)=>{a?d("unread",[l]):d("read",[l])};return(a,l)=>{const N=A("IconBtn");return c(),h(N,{id:"notification-btn"},{default:s(()=>[t(L,I(o.badgeProps,{"model-value":o.notifications.some(e=>!e.isSeen),color:"error",dot:"","offset-x":"2","offset-y":"3"}),{default:s(()=>[t(g,{icon:"tabler-bell"})]),_:1},16,["model-value"]),t(F,{activator:"parent",width:"380px",location:o.location,offset:"12px","close-on-content-click":!1},{default:s(()=>[t(Y,{class:"d-flex flex-column"},{default:s(()=>[t(j,{class:"notification-section"},{append:s(()=>[b(t(O,{size:"small",color:"primary",class:"me-2"},{default:s(()=>[_(m(p(i))+" New ",1)]),_:1},512),[[V,o.notifications.some(e=>!e.isSeen)]]),b(t(N,{size:"34",onClick:k},{default:s(()=>[t(g,{size:"20",color:"high-emphasis",icon:p(v)?"tabler-mail-opened":"tabler-mail"},null,8,["icon"]),t(W,{activator:"parent",location:"start"},{default:s(()=>[_(m(p(v)?"Mark all as read":"Mark all as unread"),1)]),_:1})]),_:1},512),[[V,o.notifications.length]])]),default:s(()=>[t(H,{class:"text-h6"},{default:s(()=>l[0]||(l[0]=[_(" Notifications ")])),_:1,__:[0]})]),_:1}),t(C),t(p(D),{options:{wheelPropagation:!1},style:{"max-block-size":"23.75rem"}},{default:s(()=>[t(X,{class:"notification-list rounded-0 py-0"},{default:s(()=>[(c(!0),y(B,null,U(o.notifications,(e,z)=>(c(),y(B,{key:e.title},[z>0?(c(),h(C,{key:0})):x("",!0),t(R,{link:"",lines:"one","min-height":"66px",class:"list-item-hover-class",onClick:S=>a.$emit("click:notification",e)},{default:s(()=>[u("div",ee,[t(G,{color:e.color&&!e.img?e.color:void 0,variant:e.img?void 0:"tonal"},{default:s(()=>[e.text?(c(),y("span",te,m(("avatarText"in a?a.avatarText:p(Z))(e.text)),1)):x("",!0),e.img?(c(),h(J,{key:1,src:e.img},null,8,["src"])):x("",!0),e.icon?(c(),h(g,{key:2,icon:e.icon},null,8,["icon"])):x("",!0)]),_:2},1032,["color","variant"]),u("div",null,[u("p",ae,m(e.title),1),u("p",se,m(e.subtitle),1),u("p",oe,m(e.time),1)]),t(K),u("div",ie,[t(g,{size:"10",icon:"tabler-circle-filled",color:e.isSeen?"#a8aaae":"primary",class:M([`${e.isSeen?"visible-in-hover":""}`,"mb-2"]),onClick:$(S=>r(e.isSeen,e.id),["stop"])},null,8,["color","class","onClick"]),t(g,{size:"20",icon:"tabler-x",class:"visible-in-hover",onClick:S=>a.$emit("remove",e.id)},null,8,["onClick"])])])]),_:2},1032,["onClick"])],64))),128)),b(t(R,{class:"text-center text-medium-emphasis",style:{"block-size":"56px"}},{default:s(()=>[t(q,null,{default:s(()=>l[1]||(l[1]=[_("No Notification Found!")])),_:1,__:[1]})]),_:1},512),[[V,!o.notifications.length]])]),_:1})]),_:1}),t(C),b(t(Q,{class:"pa-4"},{default:s(()=>[t(P,{block:"",size:"small"},{default:s(()=>l[2]||(l[2]=[_(" View All Notifications ")])),_:1,__:[2]})]),_:1},512),[[V,o.notifications.length]])]),_:1})]),_:1},8,["location"])]),_:1})}}}),re="/build/assets/avatar-3-BxDW4ia1.png",ne="/build/assets/avatar-4-CtU30128.png",ce="/build/assets/avatar-5-CmycerLe.png",de="/build/assets/paypal-rounded-DUgvboRY.png",we=T({__name:"NavBarNotifications",setup(f){const n=E([{id:1,img:ne,title:"Congratulation Flora! 🎉",subtitle:"Won the monthly best seller badge",time:"Today",isSeen:!0},{id:2,text:"Tom Holland",title:"New user registered.",subtitle:"5 hours ago",time:"Yesterday",isSeen:!1},{id:3,img:ce,title:"New message received 👋🏻",subtitle:"You have 10 unread messages",time:"11 Aug",isSeen:!0},{id:4,img:de,title:"PayPal",subtitle:"Received Payment",time:"25 May",isSeen:!1,color:"error"},{id:5,img:re,title:"Received Order 📦",subtitle:"New order received from john",time:"19 Mar",isSeen:!0}]),o=i=>{n.value.forEach((r,a)=>{i===r.id&&n.value.splice(a,1)})},d=i=>{n.value.forEach(r=>{i.forEach(a=>{a===r.id&&(r.isSeen=!0)})})},v=i=>{n.value.forEach(r=>{i.forEach(a=>{a===r.id&&(r.isSeen=!1)})})},k=i=>{i.isSeen||d([i.id])};return(i,r)=>{const a=le;return c(),h(a,{notifications:p(n),onRemove:o,onRead:d,onUnread:v,"onClick:notification":k},null,8,["notifications"])}}});export{we as default};
