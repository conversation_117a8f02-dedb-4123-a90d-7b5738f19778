import{V as i,b as n}from"./VCard-y1UgCbHQ.js";import{V as l}from"./VCardText-DnBTLtJ0.js";import{d as s,c as m,b as e,f as o,o as d,s as p,e as a}from"./main-CWjS3hwz.js";import{b as r}from"./route-block-B_A1xBdJ.js";import"./VAvatar-Ds2UYUGl.js";import"./VImg-B7Lnz0wk.js";const f=s({__name:"admin",setup(u){return(_,t)=>(d(),m("div",null,[e(i,null,{default:o(()=>[e(n,null,{default:o(()=>t[0]||(t[0]=[p("Administrator")])),_:1,__:[0]}),e(l,null,{default:o(()=>t[1]||(t[1]=[a("p",null,"Administrator functionality will be implemented here.",-1),a("p",null,"This page will provide administrative controls and system configuration.",-1)])),_:1,__:[1]})]),_:1})]))}});typeof r=="function"&&r(f);export{f as default};
