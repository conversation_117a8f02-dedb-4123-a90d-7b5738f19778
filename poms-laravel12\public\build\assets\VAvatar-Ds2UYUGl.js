import{a8 as o,af as c,bb as f,J as V,bP as y,a9 as k,aw as P,bu as C,aA as S,aB as b,ah as h,bJ as z,ac as A,b as l,aF as B,an as I,ae as x,aU as D,ao as F,aJ as J,aV as R,aq as T,aK as _,X as q,aL as w}from"./main-CWjS3hwz.js";import{V as K}from"./VImg-B7Lnz0wk.js";function U(a){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"div",s=arguments.length>2?arguments[2]:void 0;return o()({name:s??f(y(a.replace(/__/g,"-"))),props:{tag:{type:String,default:r},...c()},setup(e,u){let{slots:t}=u;return()=>{var n;return V(e.tag,{class:[a,e.class],style:e.style},(n=t.default)==null?void 0:n.call(t))}}})}const L=k({start:<PERSON><PERSON>an,end:<PERSON><PERSON><PERSON>,icon:T,image:String,text:String,...R(),...c(),...J(),...F(),...D(),...x(),...I(),...B({variant:"flat"})},"VAvatar"),X=o()({name:"VAvatar",props:L(),setup(a,r){let{slots:s}=r;const{themeClasses:e}=P(a),{borderClasses:u}=C(a),{colorClasses:t,colorStyles:n,variantClasses:i}=S(a),{densityClasses:m}=b(a),{roundedClasses:d}=h(a),{sizeClasses:v,sizeStyles:g}=z(a);return A(()=>l(a.tag,{class:["v-avatar",{"v-avatar--start":a.start,"v-avatar--end":a.end},e.value,u.value,t.value,m.value,d.value,v.value,i.value,a.class],style:[n.value,g.value,a.style]},{default:()=>[s.default?l(w,{key:"content-defaults",defaults:{VImg:{cover:!0,src:a.image},VIcon:{icon:a.icon}}},{default:()=>[s.default()]}):a.image?l(K,{key:"image",src:a.image,alt:"",cover:!0},null):a.icon?l(q,{key:"icon",icon:a.icon},null):a.text,_(!1,"v-avatar")]})),{}}});export{X as V,U as c};
